dbconf:
     # 数据库类型 mysql, sqlite3, post<PERSON><PERSON>, sqlserver
     driver: mysql
     #服务器地址 本地建议 127.0.0.1
     hostname: ************
     #端口 默认3306
     hostport: 3306
     #用户名
     username: root
     #密码
     password: root
     #数据库名
     database: fincore
     #数据表前缀
     prefix: 
redis:
     host: ************ # 连接地址
     port: 6379         # 端口
     password:          # 密码
     db: 0              # 数据库编号
     timeout: 15        # 链接超时 单位秒
jwt:
     secret: 3Bde3BGEbYqtqyEUzW3ry8jKFcaPH17fRmTmqE7MDr05Lwj95uruRKrrkb44TJ4s
     jwt_ttl: 43200
app:
     #版本号
     version: 1.3.0
     #环境状态：dev=开发，pro=生产
     env: dev
     #运行服务端口（根据您的实际情况修改）
     port: 8108
     #运行H5服务的端口（根据您的实际情况修改）
     h5port: 444
     #运行服务器的IP地址
     hostname: ************
     #接口合法性验证
     apisecret: fincore@kk888
     #接口JWT验证、跨域域名-不添加请求时报403 (开发、部署必改)
     allowurl: http://************:9105,http://************:9106,http://************:444,http://localhost:6020,http://localhost:9106,http://localhost:*,http://************:*
     #后台 token 超时时间单位(分钟)
     tokenouttime: 1440
     # 客户端 token 过期时间(分钟)
     clientTokenOutTime: 1440
     #调用cpu个数
     cpunum: 3
     # Gin 框架在运行的时候默认是debug模式 有： 开发：debug，生产：release，测试模式：test
     runlogtype: debug
     # 配置代码生成时-前端代码根目录位置(开发必改)
     vueobjroot: D:/Project/develop/vue/gofly_enterprise/business
     #配置企业私有仓网址
     companyPrivateHouse: 
     # 配置根域名访问重定向路径,默认是业务端后台
     rootview: webbusiness
     #不需要token-根模块
     noVerifyTokenRoot: resource,webbusiness,webadmin
     #不需要api接口合法性验证-根模块md5加密
     noVerifyAPIRoot: resource,webbusiness,webadmin,uniapp
     #不需要验证token-具体请求路径
     noVerifyToken: /common/uploadfile/get_image,/common/install/index,/common/install/save,/admin/user/login,/admin/user/logout,/admin/user/refreshtoken,/admin/user/get_code,/admin/user/resetPassword,/business/user/login,/business/user/logout,/business/user/refreshtoken,/business/user/get_code,/business/user/resetPassword,/admin/user/get_logininfo,/business/user/get_logininfo,/uniapp/user/loginBySms,/business/captcha/getCaptcha,/business/user/postSms,/uniapp/captcha/getCaptcha,/uniapp/user/postSms,/uniapp/user/postBySms,/uniapp/user/postBySms,/uniapp/user/getUserInfo,/business/payment/manager/disbursementCallback,/business/payment/manager/PaymentCallback
     #不需要接口合法性-具体请求路径
     noVerifyAPI: /common/install/index,/common/install/save,/business/payment/manager/disbursementCallback
     #邀请页面路径
     invitationPage: /#/pages/login/login
     #前端请求协议
     h5Protocol: http 
     # 文件服务地址，后续要改成 oss
     fileServer: http://192.168.0.12:8108

# ==================== 阿里云OSS配置 ====================
# 阿里云对象存储服务配置，用于文件上传和存储
oss:
  # 是否启用OSS (true/false，如果为false则保存在服务器的目录上，开发环境建议false)
  enabled: false
  # OSS访问域名端点
  endpoint: oss-cn-heyuan.aliyuncs.com
  # 访问密钥ID
  accessKeyId: your-access-key-id
  # 访问密钥Secret
  accessKeySecret: your-access-key-secret
  # 存储桶名称
  bucketName: fincore
  # 文件存储基础路径
  basePath: uploads/
  # 自定义域名（可选，用于文件访问）
  domain: https://your-custom-domain.com
# ==================== 开发环境日志系统配置 ====================
# 开发环境专用配置，启用详细的调试信息和SQL日志
log:
     # 开发环境使用debug等级，记录详细的调试信息
     # debug: 包含所有调试信息，便于开发调试
     level: debug

     # 日志根目录，所有日志文件都存放在此目录下
     # 目录结构: ./log/YYYY-MM/YYYY-MM-DD_模块名.log
     root_dir: ./log

     # 开发环境使用JSON格式，便于日志分析工具处理
     format: json

     # 日志输出方式，both: 同时输出到文件和控制台，file: 只输出到文件，console: 只输出到控制台
     output: both

     # 时区设置，支持标准时区名称如：Asia/Shanghai, UTC, America/New_York, Europe/London 等
     timezone: Asia/Shanghai

     # 开发环境显示代码行号，便于快速定位问题
     show_line: true

     # 开发环境日志轮转配置（相对宽松）
     max_backups: 10    # 保留的旧日志文件数量
     max_size: 100      # 单个日志文件最大大小（MB）
     max_age: 30        # 旧日志文件保留天数
     compress: true     # 压缩旧日志文件节省空间
sms:
     # 短信平台
     platform: dahantc
     # 短信平台账号
     code_account: dh36925
     # 短信平台密码
     code_password: 7f372dbc69aa4b7582cc7a13329509fc
     business_account: dh36926
     business_password: 4904bdcfa78c951709ef17470863d0ac
     # 短信平台url
     apiurl: http://www.dh3t.com/json/sms/BatchSubmit
     # 短信平台签名
     sign:
     #是否启用第三方接口 T启用 F不启用
     isused: F
vbank:
     # 四要素验证第三方API
     apiurl: "http://v.juhe.cn/verifybankcard4/query"
     # 四要素验证第三方API key (购买后获取)
     apikey: ""



# 爱签的基本配置信息
aiqian_envs:
     app_id: "*********"
     pro_host: https://oapi.asign.cn
     privateKey: MIIEwAIBADANBgkqhkiG9w0BAQEFAASCBKowggSmAgEAAoIBAQC2uFczQgnjOnYRATAG84UV6ZTojzj7/BCnKMqCIlFWYd8WFrmqpOXVmZLRDUjNk33YHPGwEA7l/dc/RA5p2pmNMAHgJVhZbcTV6xyjeU6LDkjyev5XJykruwxS5Y2+EyL4i/pcG6o/A+pI77H7fWNOEPini7GxgjD4bZXv8rJjwYOxE6gcwtnXb7aauRX8ezI62k7g2OUvXRYiZy6oRZoPABWSn/zEN+q3gfGpOFYtozuaUNe/+b1S/sMnts7v11rsgN0UDf/u+DDUVFe924lmeSW21r90oYQyM6XXb8SY2AReu+u8oCGwuIu02G1GlEyTSUjAONEKCGxwqGzZvw/PAgMBAAECggEBAKXSXbCy+e4xm/yKq19jmR/tv6necMSeWS6aok2/fzl50M9nCFCJHdvfZ5I5EB0hAVAj1GMH771hxPoxdTMzo66yJsGYorlmGQBaQr9I07L239TPMgs+CusY8XI5yYz6KP6PakI1CSfvEavnfArUHE84r7C94iFKGc8bBLuh8ar84u2y23HgmIv9I3Zss0quqoarCd6PNI/tyw7NaDpFPUmUOWti3PsgEQhgVo2DCM3WPHPfylPXQ43hZEudkho+HzIE4ztpYXHUOWGLTF+Q5nInNh1MgqxGcBuJuPAWomUk7prSMCav9Kht6Rflnmcw+HoPmWse9zr9ICdECgiygmECgYEA+wjxufMh1p4obHWvTxQpPZLCGsPF8g+WbMDVmmVA3uM6BHMfs3VcFetN1AWu879SLInC5uYC4UC+josAHpfObFEx6pzxja4Zn1Pshp+ArTBFZ/u9jmvvOXSYUoFuOVIZslaX8Ki0/N20ym5RvdFaFx0WwqYBZMjltn5XsT1WDS0CgYEAulWAFdg+b0Z6B9OZ13kSjrEO0WbGhu+gh8Dy8hQqJUjdLSvYMebLY2eHmxL5xG0vN7ARteJ62RZ7bDgFObyQbIOidigz7TiHeId++5SlzBnFCNRPX6eAZTZBfCq90ZveCQhcZ8hIni957RXF5XtBi3Kdqur9EHHgHnPE9ZyqhmsCgYEA0fv1V3oNAB1j6vW2IwvWQ28Tdpf0aDqptWbIRlIUJV0lFrvF9LNix+MAQy5N3g5XinHh2orkNc+Wll2nR+/r96cjfgCx/bV4MVJeM24QkM4kAIsPUKbwgLsK/1jM/p2yaP8OMXytiCdcJ0iIj6MjHNp0Q3XhDJEPtcuRRuzrojECgYEAia8n5/xTlhGzlhjrMmaKKdn3IxAYXhiuu+D9I5d21PoURI6DP8xUOW2ErDfHSzeKjlGRpJ5nPAX6ySpT4ifNaAGUiE6IoB8HKy6jy+443KmmCDIpPHseyqrelItYm4va8z20WhOKZSibpW5TPpBnDE1y55qfyAj9HENbJEnRT2UCgYEAxJhATzaX7KSd6FuHlE7byQTMSTSarPjVchAWH8exIIVFKO8SELP1OBibSd1Obuaeh/LxmTr+CPVGOWYAyu3KqT0D5QXEDnd0Wb16Rjfjudi4cogmay0MsxzM5SK76nV3G+HUvXaKZrtqg1zq5D4sPSRFbXhIZinTshmK6ysRxcU=
 # 签署信息配置(模板信息、商户信息)
aiqian_sign:
     # 签约地址
     sign_address: "南宁市青秀区"
     templates: 
          # 贷款告知书模板
          loan_notice_template_no: "TN19E7609B009F4B6E82F98230F69E97B2"
          # 借款合同模板
          loan_contract_template_no: "TN8723D6E923C1446BA2D17D3E6B4997FD"
          # 担保协议模板
          guarantee_template_no: "TNE462B6A508194CE094BB2DBB414A44DB"
          # 承诺书模板
          entrust_guarantee_template_no: "TN0F9F4D87D35244659F318E07DD597653"
          # 债权转让通知书模板
          creditor_assignment_notice_template_no: "TNC7A826CED4484113BE7472B87437CBD5"
     aiqian_merchants:
          # 小贷公司
          small_loan_merchant:
               # 商户名称
               mer_name: "南宁市金沙小额贷款有限公司"
               # 法人姓名
               legal_rep: "钟海斌"
               # 公司地址
               address: "南宁市青秀区凤岭南路16号保利领秀广场1号楼十九层1907、1908号办公"
               # 公司电话
               telephone: "/"
               # 户名
               account_name: "/"
               # 账号
               bank_account: "/"
               # 开户行
               bank_name: "/"
               # 签约印章认证编号
               sign_seal_account: "ASIGN914501000527452668"
          # 担保公司
          guarantor_merchant:
               # 商户名称
               mer_name: "武汉盛唐融资担保有限公司"
               # 法人姓名
               legal_rep: "/"
               # 公司地址
               address: "/"
               # 公司电话
               telephone: "/"
               # 户名
               account_name: "/"
               # 账号
               bank_account: "/"
               # 开户行
               bank_name: "/"
               # 签约印章认证编号
               sign_seal_account: "ASIGN91420103796300796R"
          # 资管公司
          asset_merchant:
               # 商户名称
               mer_name: "武汉市几何资产投资管理有限公司"
               # 法人姓名
               legal_rep: "詹学杰"
               # 公司地址
               address: "湖北省武汉市武昌区普提金国际金融中心10幢1单元5204 "
               # 公司电话
               telephone: "/"
               # 户名
               account_name: "武汉市几何资产投资管理有限公司"
               # 账号
               bank_account: "***************"
               # 开户行
               bank_name: "招商银行广州丰兴支行"
               # 签约印章认证编号
               sign_seal_account: "ASIGN91420106MAEGR0E11T"


# 商盟统统付
sumpay:
     environment: "test"  # test, prod
     test_url: "https://test.sumpay.cn/entrance/gateway.htm"
     prod_url: "https://entrance.sumpay.cn/gateway.htm"
     # 融担业务配置 - 资管和担保使用不同的商户号
     # 小贷商户
     small_loan_merchant:
          mer_name: "南宁市金沙小额贷款有限公司"
          mer_no: "s100000040"
          app_id: "s100000040"
          passwd: "sumpay"
          pfx_path: "resource/cert/yixuntiankong.pfx"
     # 资管商户
     asset_merchant:
          mer_name: "武汉市几何资产投资管理有限公司"
          mer_no: "s100000040"
          app_id: "s100000040"
          passwd: "sumpay"
          pfx_path: "resource/cert/yixuntiankong.pfx"
     # 担保商户
     guarantee_merchant:
          mer_name: "武汉盛唐融资担保有限公司"
          mer_no: "s100000040"
          app_id: "s100000040"
          passwd: "sumpay"
          pfx_path: "resource/cert/yixuntiankong.pfx"
     skip_tls_verify: true      # 是否跳过TLS验证（测试环境建议true）
     request_timeout: 30        # 请求超时时间（秒）
     retry_count: 3             # 重试次数
     retry_wait_time: 5         # 重试等待时间（秒）
     cert:
          public_key_path: "resource/cert/yixun.cer"

# ==================== 锁配置 ====================
lock:
  # 锁类型: memory(内存锁,开发环境推荐) 或 redis(Redis分布式锁，生产环境推荐)
  type: memory

  # Redis锁配置
  redis:
    # 默认锁过期时间（秒）
    default_expiration: 30
    # 看门狗续期间隔（秒）
    renewal_interval: 10
    # 最大重试次数
    max_retry_times: 3
    # 重试间隔（毫秒）
    retry_interval_ms: 100
    # 是否启用看门狗自动续期
    enable_watchdog: true