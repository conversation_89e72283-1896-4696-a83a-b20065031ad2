package productrules

import (
	"encoding/json"
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/log"
	"fincore/utils/repayment"
	"fincore/utils/results"
	"fmt"
	"io"
	"reflect"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
)

// 用于自动注册路由
type List struct {
}

// 初始化生成路由
func init() {
	fpath := List{}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

// ProductRule 产品规则结构体
type ProductRule struct {
	ProductRule       *model.ProductRules          `json:"product_rule"`
	CalculationResult *repayment.RepaymentSchedule `json:"calculation_result"` // 计算结果
}

func (api *List) CreateRule(c *gin.Context) {

	var rule ProductRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		results.Failed(c, "创建产品规则失败", err.Error())
		return
	}

	// 校验请求数据
	schema := CreateProductRuleSchema()
	validator := jsonschema.NewValidator(schema)
	validatorMap := gconv.Map(rule.ProductRule)
	validationResult := validator.Validate(validatorMap)
	if !validationResult.Valid {
		results.Failed(c, "创建产品规则失败", validationResult.Errors)
		return
	}

	service := NewProductRuleService(c)
	err := service.CreateProductRule(&rule)
	if err != nil {
		results.Failed(c, "创建产品规则失败", err.Error())
		return
	}
	results.Success(c, "创建产品规则成功", nil, nil)

}

// 查询产品规则列表
func (api *List) GetRules(c *gin.Context) {
	// 处理查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	name := c.DefaultQuery("rule_name", "")
	period := c.DefaultQuery("loan_period", "")
	category := c.DefaultQuery("rule_category", "")

	query := model.DB().Table("product_rules")

	// 动态添加条件
	if name != "" {
		query = query.Where("rule_name", name)
	}

	if period != "" {
		query = query.Where("loan_period", period)
	}

	if category != "" {
		query = query.Where("rule_category", category)
	}

	// if repaymentMethod != "" {
	// 	query = query.Where("repayment_method", repaymentMethod)
	// }

	// 分页查询
	var total int64
	total, _ = query.Count("*")

	offset := (page - 1) * limit
	list, err := query.Fields("id, rule_name, loan_amount, loan_period, total_periods, guarantee_fee, annual_interest_rate, other_fees, rule_category").Limit(limit).Offset(offset).Order("id desc").Get()

	if err != nil {
		log.Error(fmt.Sprintf("查询产品规则失败：%s", err))
		results.Failed(c, "查询产品规则失败", nil)
		return
	} else {
		log.Info(fmt.Sprintf("查询产品规则成功："))
	}

	results.Success(c, "查询产品规则成功", map[string]interface{}{
		"page":     page,
		"pageSize": limit,
		"total":    total,
		"items":    list}, nil)
}

// 删除产品规则
func (api *List) DelRule(c *gin.Context) {
	body, _ := io.ReadAll(c.Request.Body)
	var parameter map[string]interface{}
	_ = json.Unmarshal(body, &parameter)
	ids := parameter["ids"]

	ids = gconv.Uints(ids)

	service := NewProductRuleService(c)
	CanDelIDs, err := service.DelProducts(ids.([]uint))
	if err != nil {
		results.Failed(c, "删除产品规则失败", err.Error())
		return
	}

	results.Success(c, "删除成功", map[string][]uint{
		"del_ids": CanDelIDs,
	}, nil)

}

// UpdateRule 编辑产品规则
func (api *List) UpdateRule(c *gin.Context) {
	// 解析请求参数
	var rule ProductRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		results.Failed(c, "编辑产品规则失败", err.Error())
		return
	}

	if rule.ProductRule.ID == 0 {
		results.Failed(c, "编辑产品规则失败", "不是有效的产品ID")
		return
	}

	// 数据校验
	schema := UpdateProductRuleSchema()
	validator := jsonschema.NewValidator(schema)
	validatorMap := gconv.Map(rule.ProductRule)
	validationResult := validator.Validate(validatorMap)
	if !validationResult.Valid {
		results.Failed(c, "编辑产品参数校验失败", validationResult.Errors)
		return
	}

	service := NewProductRuleService(c)
	err := service.UpdateProductRule(&rule)
	if err != nil {
		results.Failed(c, "编辑产品规则失败", err.Error())
		return
	}

	// 更新成功响应
	results.Success(c, "产品规则更新成功", gin.H{
		"id":   rule.ProductRule.ID,
		"data": rule,
	}, nil)
}

// GetRuleDetail 获取产品详情
func (api *List) GetRuleDetail(c *gin.Context) {
	ID := gconv.Uint(c.Query("id"))

	if ID == 0 {
		results.Failed(c, "获取产品规则失败", "ID不能为空")
		return
	}

	service := NewProductRuleService(c)
	rule, err := service.GetProductRuleByID(int(ID))
	if err != nil {
		results.Failed(c, "获取产品规则失败", err.Error())
		return
	}

	results.Success(c, "获取产品规则成功", rule, nil)
}
