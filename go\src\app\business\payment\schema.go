package payment

import "fincore/utils/jsonschema"

// RefundRequest 退款请求结构
type RefundRequest struct {
	TransactionID int     `json:"transaction_id" validate:"required"` // 流水记录ID
	RefundAmount  float64 `json:"refund_amount" validate:"required"`  // 退款金额
	RefundReason  string  `json:"refund_reason" validate:"required"`  // 退款原因
}

// RefundResponse 退款响应结构
type RefundResponse struct {
	RefundTransactionNo string  `json:"refund_transaction_no"` // 退款流水号
	RefundAmount        float64 `json:"refund_amount"`         // 退款金额
	Status              string  `json:"status"`                // 退款状态
	Message             string  `json:"message"`               // 状态描述
}

// RefreshRefundStatusRequest 刷新退款状态请求结构
type RefreshRefundStatusRequest struct {
	TransactionID int `json:"transaction_id" validate:"required"` // 流水记录ID
}

// PartialOfflinePaymentRequest 部分线下支付请求结构
type PartialOfflinePaymentRequest struct {
	BillID         int     `json:"bill_id" validate:"required"`         // 账单ID
	PaymentChannel string  `json:"payment_channel" validate:"required"` // 支付渠道文案
	Voucher        string  `json:"voucher" validate:"required"`         // 凭证图片
	Amount         float64 `json:"amount" validate:"required,gt=0"`     // 支付金额
}

// PartialOfflinePaymentResponse 部分线下支付响应结构
type PartialOfflinePaymentResponse struct {
	BillID  int     `json:"bill_id"` // 账单ID
	Amount  float64 `json:"amount"`  // 支付金额
	Status  string  `json:"status"`  // 处理状态
	Message string  `json:"message"` // 状态描述
}

// RefundCallbackRequest 退款通知回调请求结构
type RefundCallbackRequest struct {
	RespCode      string `json:"resp_code"`      // 返回码，成功为"000000"，其他为失败
	RespMsg       string `json:"resp_msg"`       // 返回消息，失败时说明原因
	SignType      string `json:"sign_type"`      // 签名类型，目前仅支持"CERT"（大写）
	Sign          string `json:"sign"`           // 数据签名，需要验证
	MerNo         string `json:"mer_no"`         // 商户代码
	RefundNo      string `json:"refund_no"`      // 退款订单号，唯一标识
	RefundTime    string `json:"refund_time"`    // 退款时间
	SerialNo      string `json:"serial_no"`      // 退款流水号
	Status        string `json:"status"`         // 退款状态 - "0":失败, "1":成功, "2":处理中
	SuccessAmount string `json:"success_amount"` // 成功退款金额，格式如"50.01"
	SuccessTime   string `json:"success_time"`   // 成功退款时间，格式如"20130520125022"
	// 可选参数
	TtfReturnCode string `json:"ttf_return_code,omitempty"` // 支付返回码（失败时可能返回）
	TtfReturnMsg  string `json:"ttf_return_msg,omitempty"`  // 支付返回信息（失败时可能返回）
}

// RefundCallbackResponse 退款通知回调响应结构
type RefundCallbackResponse struct {
	RespCode string `json:"resp_code"` // 响应码
	RespMsg  string `json:"resp_msg"`  // 响应消息
}

type PaymentCallbackRequest struct {
	CallbackBase
	MerNo           string  `json:"mer_no"`                            // 商户号
	OrderNo         string  `json:"order_no"`                          // 订单号
	OffsetAmount    float64 `json:"offset_amount"`                     // 优惠金额
	PaidAmout       float64 `json:"paid_amount"`                       // 实付金额
	TradeNo         string  `json:"trade_no"`                          // 交易流水号
	OrderTime       string  `json:"order_time"`                        // 订单时间
	Status          string  `json:"status"`                            // 状态 00 成功，01 处理中，03 失败
	SuccessTime     string  `json:"success_time"`                      // 成功时间
	SuccessAmount   string  `json:"success_amount"`                    // 成功金额
	Remark          string  `json:"remark" optional:"true"`            // 备注
	TtfReturnCode   string  `json:"ttf_return_code" optional:"true"`   // 失败时可能会返回
	TtfReturnMsg    string  `json:"ttf_return_msg" optional:"true"`    // 失败时可能会返回
	TraceCode       string  `json:"trace_code" optional:"true"`        // T0001:担保交易 T0002:即时交易
	PayProductCode  string  `json:"pay_product_code" optional:"true"`  // 支付产品码
	OffestDetail    string  `json:"offest_detail" optional:"true"`     // 优惠详情
	ChannelSerialNo string  `json:"channel_serial_no" optional:"true"` // 渠道流水号
	PeriodNum       string  `json:"period_num" optional:"true"`        // 信用卡分期支付必填，暂只有：3、6、9、12、18、24、36、60
	Attach          string  `json:"attach" optional:"true"`            // 附加信息
}

// GetRefundRequestSchema 获取退款请求验证规则
func GetRefundRequestSchema() jsonschema.Schema {
	minTransactionID := float64(1)
	minRefundAmount := float64(0.01)
	maxReasonLength := 500

	return jsonschema.Schema{
		Title: "客户退款请求",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"transaction_id": {
				Type:        "integer",
				Required:    true,
				Description: "流水记录ID",
				Min:         &minTransactionID,
			},
			"refund_amount": {
				Type:        "number",
				Required:    true,
				Description: "退款金额",
				Min:         &minRefundAmount,
			},
			"refund_reason": {
				Type:        "string",
				Required:    false,
				Description: "退款原因",
				MaxLength:   maxReasonLength,
			},
		},
	}
}

// GetRefundCallbackSchema 获取退款通知回调验证规则
func GetRefundCallbackSchema() jsonschema.Schema {
	maxLength6 := 6
	maxLength128 := 128
	maxLength10 := 10
	maxLength255 := 255
	maxLength20 := 20
	maxLength30 := 30
	maxLength14 := 14
	maxLength1 := 1

	return jsonschema.Schema{
		Title: "退款通知回调请求",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"resp_code": {
				Type:        "string",
				Required:    true,
				Description: "返回码，成功为000000，其他为失败",
				MaxLength:   maxLength6,
			},
			"resp_msg": {
				Type:        "string",
				Required:    true,
				Description: "返回消息，失败时说明原因",
				MaxLength:   maxLength128,
			},
			"sign_type": {
				Type:        "string",
				Required:    true,
				Description: "签名类型，目前仅支持CERT（大写）",
				MaxLength:   maxLength10,
			},
			"sign": {
				Type:        "string",
				Required:    true,
				Description: "数据签名",
				MaxLength:   maxLength255,
			},
			"mer_no": {
				Type:        "string",
				Required:    true,
				Description: "商户代码",
				MaxLength:   maxLength20,
			},
			"refund_no": {
				Type:        "string",
				Required:    true,
				Description: "退款订单号，唯一标识",
				MaxLength:   maxLength30,
			},
			"refund_time": {
				Type:        "string",
				Required:    true,
				Description: "退款时间",
				MaxLength:   maxLength14,
			},
			"serial_no": {
				Type:        "string",
				Required:    true,
				Description: "退款流水号",
				MaxLength:   maxLength30,
			},
			"status": {
				Type:        "string",
				Required:    true,
				Description: "退款状态 - 0:失败, 1:成功, 2:处理中",
				MaxLength:   maxLength1,
			},
			"success_amount": {
				Type:        "string",
				Required:    true,
				Description: "成功退款金额，格式如50.01",
				MaxLength:   maxLength10,
			},
			"success_time": {
				Type:        "string",
				Required:    true,
				Description: "成功退款时间，格式如20130520125022",
				MaxLength:   maxLength14,
			},
			"ttf_return_code": {
				Type:        "string",
				Required:    false,
				Description: "支付返回码（失败时可能返回）",
				MaxLength:   maxLength20,
			},
			"ttf_return_msg": {
				Type:        "string",
				Required:    false,
				Description: "支付返回信息（失败时可能返回）",
				MaxLength:   maxLength255,
			},
		},
	}
}

func GetDisbursementCallbackSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "代付回调请求",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"resp_code": {
				Type:        "string",
				Required:    true,
				Description: "响应码",
			},
			"resp_msg": {
				Type:        "string",
				Required:    true,
				Description: "响应信息",
			},
			"sign_type": {
				Type:        "string",
				Required:    true,
				Description: "签名类型",
			},
			"sign": {
				Type:        "string",
				Required:    true,
				Description: "签名",
			},
			"order_no": {
				Type:        "string",
				Required:    true,
				Description: "订单号",
			},
			"trace_no": {
				Type:        "string",
				Required:    true,
				Description: "交易流水号",
			},
			"order_amount": {
				Type:        "string",
				Required:    true,
				Description: "订单金额",
			},
			"status": {
				Type:        "string",
				Required:    true,
				Description: "状态",
			},
			"fee_amount": {
				Type:        "string",
				Required:    false,
				Description: "手续费",
			},
			"obo_type": {
				Type:        "string",
				Required:    false,
				Description: "业务类型",
			},
			"remark": {
				Type:        "string",
				Required:    false,
				Description: "备注",
			},
			"ttf_return_code": {
				Type:        "string",
				Required:    false,
				Description: "失败时可能会返回",
			},
			"ttf_return_msg": {
				Type:        "string",
				Required:    false,
				Description: "失败时可能会返回",
			},
		},
	}
}

// GetPaymentCallbackSchema 支付回调请求参数
func GetPaymentCallbackSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "支付回调请求",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"resp_code": {
				Type:        "string",
				Required:    true,
				Description: "响应码",
			},
			"resp_msg": {
				Type:        "string",
				Required:    true,
				Description: "响应信息",
			},
			"sign_type": {
				Type:        "string",
				Required:    true,
				Description: "签名类型",
			},
			"sign": {
				Type:        "string",
				Required:    true,
				Description: "签名",
			},
			"mer_no": {
				Type:        "string",
				Required:    true,
				Description: "商户号",
			},
			"order_no": {
				Type:        "string",
				Required:    true,
				Description: "订单号",
			},
			"offset_amount": {
				Type:        "string",
				Required:    true,
				Description: "优惠金额",
			},
			"paid_amount": {
				Type:        "string",
				Required:    true,
				Description: "实付金额",
			},
			"trade_no": {
				Type:        "string",
				Required:    true,
				Description: "交易流水号",
			},
			"order_time": {
				Type:        "string",
				Required:    true,
				Description: "订单时间",
			},
			"status": {
				Type:        "string",
				Required:    true,
				Description: "状态",
			},
			"success_time": {
				Type:        "string",
				Required:    true,
				Description: "成功时间",
			},
			"success_amount": {
				Type:        "string",
				Required:    true,
				Description: "成功金额",
			},
		},
	}
}

const (
	PaymentChannelAlipay     = "支付宝支付（线下）"
	PaymentChannelWechat     = "微信支付（线下）"
	PaymentChannelBankCard   = "银行卡支付（线下）"
	PaymentChannelCreditCard = "信用卡支付（线下）"
)

// GetPartialOfflinePaymentRequestSchema 获取部分线下支付请求的JSON Schema
func GetPartialOfflinePaymentRequestSchema() jsonschema.Schema {
	maxLength50 := 50
	maxLength500 := 500
	minBillID := 1.0
	minAmount := 0.01
	maxAmount := 999999.99

	return jsonschema.Schema{
		Title: "部分线下支付请求",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"bill_id": {
				Type:        "integer",
				Required:    true,
				Min:         &minBillID,
				Description: "账单ID",
			},
			"payment_channel": {
				Type:        "string",
				Required:    true,
				MaxLength:   maxLength50,
				Description: "支付渠道文案",
				Enum:        []string{PaymentChannelAlipay, PaymentChannelWechat, PaymentChannelBankCard, PaymentChannelCreditCard},
			},
			"voucher": {
				Type:        "string",
				Required:    true,
				MaxLength:   maxLength500,
				Description: "凭证图片",
			},
			"amount": {
				Type:        "number",
				Required:    true,
				Min:         &minAmount,
				Max:         &maxAmount,
				Description: "支付金额",
			},
		},
	}
}

func GetRefreshRefundStatusRequestSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "刷新退款状态请求",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"transaction_id": {
				Type:        "integer",
				Required:    true,
				Description: "流水记录ID",
			},
		},
	}
}

// CancelOfflinePaymentRequest 销账取消请求结构
type CancelOfflinePaymentRequest struct {
	TransactionNo string `json:"transaction_no" validate:"required"` // 流水号
}

// CancelOfflinePaymentResponse 销账取消响应结构
type CancelOfflinePaymentResponse struct {
	TransactionNo string  `json:"transaction_no"` // 取消的流水号
	BillID        int     `json:"bill_id"`        // 账单ID
	Amount        float64 `json:"amount"`         // 取消金额
	Status        string  `json:"status"`         // 处理状态
	Message       string  `json:"message"`        // 处理消息
}

// CalculateScheduleRequest 计算还款计划请求结构
type CalculateScheduleRequest struct {
	LoanAmount         float64 `json:"loan_amount" validate:"required,gt=0"`           // 金额
	LoanPeriod         int     `json:"loan_period" validate:"required,gt=0"`           // 借款周期(天数)
	TotalPeriods       int     `json:"total_periods" validate:"required,gt=0"`         // 期数
	GuaranteeFee       float64 `json:"guarantee_fee" validate:"required,gte=0"`        // 担保费
	AnnualInterestRate float64 `json:"annual_interest_rate" validate:"required,gte=0"` // 年利率
	OtherFees          float64 `json:"other_fees" validate:"required,gte=0"`           // 其他费用
	PrePrincipal       float64 `json:"pre_principal" validate:"required,gte=0"`        // 前置本金
	PreInterest        float64 `json:"pre_interest" validate:"required,gte=0"`         // 前置利息
	PreGuaranteeFee    float64 `json:"pre_guarantee_fee" validate:"required,gte=0"`    // 前置担保费
}

// GetCalculateScheduleRequestSchema 获取计算还款计划请求的JSON Schema
func GetCalculateScheduleRequestSchema() jsonschema.Schema {
	minLoanAmount := 0.01
	maxLoanAmount := 10000000.0
	minLoanPeriod := 1.0
	maxLoanPeriod := 3650.0 // 最大10年
	minTotalPeriods := 1.0
	maxTotalPeriods := 120.0 // 最大120期
	minFee := 0.0
	maxFee := 1000000.0
	minInterestRate := 0.0
	maxInterestRate := 100.0 // 最大100%年利率

	return jsonschema.Schema{
		Title: "计算还款计划请求参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"loan_amount": {
				Type:        "number",
				Required:    true,
				Min:         &minLoanAmount,
				Max:         &maxLoanAmount,
				Description: "贷款金额（必填，0.01-10000000）",
			},
			"loan_period": {
				Type:        "integer",
				Required:    true,
				Min:         &minLoanPeriod,
				Max:         &maxLoanPeriod,
				Description: "借款周期天数（必填，1-3650天）",
			},
			"total_periods": {
				Type:        "integer",
				Required:    true,
				Min:         &minTotalPeriods,
				Max:         &maxTotalPeriods,
				Description: "总期数（必填，1-120期）",
			},
			"guarantee_fee": {
				Type:        "number",
				Required:    true,
				Min:         &minFee,
				Max:         &maxFee,
				Description: "担保费（必填，0-1000000）",
			},
			"annual_interest_rate": {
				Type:        "number",
				Required:    true,
				Min:         &minInterestRate,
				Max:         &maxInterestRate,
				Description: "年利率（必填，0-100%）",
			},
			"other_fees": {
				Type:        "number",
				Required:    true,
				Min:         &minFee,
				Max:         &maxFee,
				Description: "其他费用（必填，0-1000000）",
			},
			"pre_principal": {
				Type:        "number",
				Required:    false,
				Description: "前置本金（可选，默认0）",
			},
			"pre_interest": {
				Type:        "number",
				Required:    false,
				Description: "前置利息（可选，默认0）",
			},
			"pre_guarantee_fee": {
				Type:        "number",
				Required:    false,
				Description: "前置担保费（可选，默认0）",
			},
		},
		Required: []string{"loan_amount", "loan_period", "total_periods", "guarantee_fee", "annual_interest_rate", "other_fees"},
	}
}

// GetCancelOfflinePaymentRequestSchema 获取销账取消请求的JSON Schema
func GetCancelOfflinePaymentRequestSchema() jsonschema.Schema {
	maxLength50 := 50
	minLength1 := 1

	return jsonschema.Schema{
		Title: "销账取消请求参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"transaction_no": {
				Type:        "string",
				Required:    true,
				MinLength:   minLength1,
				MaxLength:   maxLength50,
				Description: "流水号（必填）",
			},
		},
		Required: []string{"transaction_no"},
	}
}
