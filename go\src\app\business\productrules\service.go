package productrules

import (
	"context"
	"fincore/model"
	"fmt"

	"github.com/gogf/gf/v2/util/gconv"
	"github.com/samber/lo"
)

type ProductRuleService struct {
	ctx  context.Context
	repo *ProductRuleRepository
}

func NewProductRuleService(ctx context.Context) *ProductRuleService {
	return &ProductRuleService{
		ctx:  ctx,
		repo: NewProductRuleRepository(ctx),
	}
}

// CreateProductRule 创建产品规则
func (s *ProductRuleService) CreateProductRule(rule *ProductRule) (err error) {
	// 检查计算结果
	err = calculationResult(rule)
	if err != nil {
		return
	}
	// 插入产品规则和账单计算结果
	tx := model.DB(model.WithContext(s.ctx))
	err = s.repo.InsertProductRuleAndCalculationResult(tx, rule)
	if err != nil {
		return
	}

	return
}

// UpdateProductRule 更新产品规则
func (s *ProductRuleService) UpdateProductRule(rule *ProductRule) (err error) {
	// 检查是否有在途订单
	list, err := s.repo.GetProductRuleInProgressOrders(model.DB(model.WithContext(s.ctx)), GetProductRuleInProgressOrdersParams{IDs: []uint{uint(rule.ProductRule.ID)}})
	if err != nil {
		err = fmt.Errorf("查询产品是否存在在途订单失败: %w", err)
		return
	}

	if len(list) > 0 {
		err = fmt.Errorf("产品存在在途订单, 暂不能编辑")
		return
	}

	// 检查计算结果
	err = calculationResult(rule)
	if err != nil {
		return
	}

	tx := model.DB(model.WithContext(s.ctx))
	err = s.repo.UpdateProductRuleAndCalculationResult(tx, rule)
	if err != nil {
		return fmt.Errorf("更新产品规则和规则账单计算结果失败: %w", err)
	}

	return
}

// DelProducts 删除产品
func (s *ProductRuleService) DelProducts(IDs []uint) (CanDelIDs []uint, err error) {
	list, err := s.repo.GetProductRuleInProgressOrders(model.DB(model.WithContext(s.ctx)), GetProductRuleInProgressOrdersParams{IDs: IDs})
	if err != nil {
		err = fmt.Errorf("查询产品是否存在在途订单失败: %w", err)
		return
	}

	orderPIDs := make([]uint, len(list))
	for i, item := range list {
		orderPIDs[i] = gconv.Uint(item["product_rule_id"])
	}
	// 去重
	orderPIDs = lo.Uniq(orderPIDs)
	// 取差集
	CanDelIDs, _ = lo.Difference(IDs, orderPIDs)

	if len(CanDelIDs) == 0 {
		err = fmt.Errorf("删除的产品都存在在途订单, IDs: %v", orderPIDs)
		return
	}

	err = s.repo.DelProducts(model.DB(model.WithContext(s.ctx)), CanDelIDs)
	if err != nil {
		err = fmt.Errorf("删除产品失败: %w", err)
		return
	}

	return
}

// GetProductRuleByID 根据ID获取产品规则
func (s *ProductRuleService) GetProductRuleByID(ID int) (rule *ProductRule, err error) {
	rule, err = s.repo.GetProductRuleByID(ID)
	if err != nil {
		return
	}

	return
}
