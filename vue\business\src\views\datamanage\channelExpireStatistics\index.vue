<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import {
  IconRefresh,
  IconSearch,
  IconUserAdd,
} from '@arco-design/web-vue/es/icon';
import dayjs from 'dayjs';
import { getChannelDueStatistics } from '@/api/datamanage';
import { getChannelList } from '@/api/channel';
const queryFormRef = ref();
const queryForm = reactive({
  channel_id: '',
  due_date_start: '',
  due_date_end: '',
  period_number: '',
  is_new_user: '',
});
const due_date = ref<any>([]);
const handleDueDateChange = (val: any) => {
  if (val && val.length) {
    queryForm.due_date_start = val[0];
    queryForm.due_date_end = val[1];
  }else {
    queryForm.due_date_start = '';
    queryForm.due_date_end = '';
  }
}
const loading = ref(false);
const statistics = ref<any>({})
const fetchData = async () => {
  loading.value = true;
  getChannelDueStatistics({
    ...queryForm,
    page: pagination.current,
    page_size: pagination.pageSize,
  }).then((res) => {
    statistics.value = res.statistics;
    dataSource.value = res.pagination.data;
    pagination.total = res.pagination.total;
  }).finally(() => {
    loading.value = false;
  })
};

const dictChannels = ref<any>([]);
onMounted(() => {
  fetchData();
  getChannelList({ page: 1, pageSize: 1000 }).then(res => {
    dictChannels.value = res.data;
  })
})

const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    width: 50,
    render: ({ rowIndex }: any) => {
      return rowIndex + 1;
    }
  },
  {
    title: '渠道',
    dataIndex: 'channel_name',
    width: 100,
  },
  {
    title: '到期帐单数',
    dataIndex: 'due_bills_count',
    width: 120,
  },
  {
    title: '新用户帐单数',
    dataIndex: 'new_user_bills',
    width: 150,
  },
  {
    title: '老用户帐单数',
    dataIndex: 'old_user_bills',
    width: 150,
  },
  {
    title: '到期金额数',
    dataIndex: 'due_amount',
    width: 120,
  },
  {
    title: '新用户到期金额数',
    dataIndex: 'new_user_due_amount',
    width: 150,
  },
  {
    title: '老用户到期金额数',
    dataIndex: 'old_user_due_amount',
    width: 150,
  },
  {
    title: '账单件均',
    dataIndex: 'avg_bill_amount',
    width: 100,
  },
  {
    title: '金额回款率',
    dataIndex: 'repayment_rate',
    width: 120,
  },
  {
    title: '新用户金额回款率',
    dataIndex: 'new_user_repayment_rate',
    width: 150,
  },
  {
    title: '老用户金额回款率',
    dataIndex: 'old_user_repayment_rate',
    width: 150,
  },
  {
    title: '还款人数',
    dataIndex: 'repayment_users',
    width: 120,
  },
  {
    title: '还款复购人数',
    dataIndex: 'repeat_purchase_users',
    width: 150,
  },
  {
    title: '复购率',
    dataIndex: 'repeat_purchase_rate',
    width: 150,
  }
];
const dataSource = ref([{}]);
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};
const handleReset = async () => {
  queryFormRef.value.resetFields();
  pagination.current = 1;
  due_date.value = [];
  queryForm.due_date_start = '';
  queryForm.due_date_end = '';
  fetchData();
};
</script>

<template>
  <div class="container">
    <!-- 渠道到期统计 -->
    <a-card title="渠道到期统计" :bordered="false">
      <div class="stat-grid">
        <a-row :gutter="[24, 12]">
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">到期帐单数</div>
              <div class="stat-number">{{ statistics.total_due_bills || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">新用户帐单数</div>
              <div class="stat-number">{{ statistics.new_user_bills || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">老用户帐单数</div>
              <div class="stat-number">{{ statistics.old_user_bills || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">到期金额数</div>
              <div class="stat-number">￥{{ statistics.total_due_amount || 0 }}</div>
            </div>
          </a-col>

          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">新用户到期金额数</div>
              <div class="stat-number">￥{{ statistics.new_user_due_amount || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">老用户到期金额数</div>
              <div class="stat-number">￥{{ statistics.old_user_due_amount || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">金额回款率</div>
              <div class="stat-number">{{ statistics.total_repayment_rate || 0 }}%</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">新用户金额回款率</div>
              <div class="stat-number">{{ statistics.new_user_repayment_rate || 0 }}%</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">老用户金额回款率</div>
              <div class="stat-number">{{ statistics.old_user_repayment_rate || 0 }}%</div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 渠道到期统计表单 -->
      <div class="due-form">
        <a-form
          ref="queryFormRef"
          :model="queryForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
          auto-label-width
          @submit="handleSearch"
        >
          <a-row :gutter="[24, 0]">
            <a-col :md="6" :sm="12">
              <a-form-item field="channel_id" label="渠道来源">
                <a-select allowClear v-model="queryForm.channel_id" placeholder="渠道来源">
                  <a-option v-for="item in dictChannels" :key="item.id" :value="item.id">{{ item.channel_name }}</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item label="到期日期">
                <a-range-picker
                  allowClear
                  :shortcuts="[
                    {
                      label: '近一周',
                      value: () => [dayjs().subtract(1, 'week'), dayjs()],
                    },
                    {
                      label: '近一月',
                      value: () => [dayjs().subtract(1, 'month'), dayjs()],
                    }
                  ]"
                  v-model="due_date"
                  @change="handleDueDateChange"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="period_number" label="期数">
                <a-input allow-clear v-model="queryForm.period_number" placeholder="期数"></a-input>
              </a-form-item>
            </a-col>
<!--            <a-col :md="6" :sm="12">
              <a-form-item label="设备来源">
                <a-select v-model="queryForm.name" placeholder="设备来源">
                  <a-option>112312</a-option>
                  <a-option>123123</a-option>
                </a-select>
              </a-form-item>
            </a-col>-->
            <a-col :md="6" :sm="12">
              <a-form-item field="is_new_user" label="新老用户">
                <a-select allow-clear v-model="queryForm.is_new_user" placeholder="新老用户">
                  <a-option value="0">老用户</a-option>
                  <a-option value="1">新用户</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  <template #icon>
                    <icon-search />
                  </template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <!-- 渠道到期统计列表 -->
    <a-card class="table-card" title="渠道到期统计列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="columns"
        :data="dataSource"
        :pagination="pagination"
        :bordered="{headerCell:true}"
        size="medium"
        :scroll="{ y: '100%' }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :loading="loading"
      >
      </a-table>
    </a-card>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}

.stat-grid {
  .arco-col {
    height: 80px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background-color: var(--color-bg-1);
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .stat-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-2);
    }

    .stat-number {
      font-size: 16px;
      color: rgb(var(--primary-6));
      margin-top: 10px;
      font-weight: bold;
    }
  }
}

.due-form {
  margin-top: 20px;
}

.table-card {
  margin-top: 10px;
}
</style>