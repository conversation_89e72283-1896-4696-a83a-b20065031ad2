package hetong

import (
	"context"
	"encoding/json"
	"errors"
	Config "fincore/app/dianziqian/config"
	"fincore/app/dianziqian/service"
	"fincore/global"
	"fincore/model"
	"fincore/utils/log"
	"fincore/utils/repayment"
	"fmt"
	"strings"
	"time"
)

// GetAssetManagement 获取资管公司信息
func GetAssetManagement() Company {
	return Company{
		Name:        AssetMgmtName,
		LegalRep:    AssetMgmtLegalRep,
		Address:     AssetMgmtAddress,
		Telephone:   AssetMgmtTelephone,
		AccountName: AssetMgmtAccountName,
		BankName:    AssetMgmtBankName,
		BankAccount: AssetMgmtBankAccount,
	}
}

// GetGuarantor1 获取担保公司1信息
func GetGuarantor1() Company {
	return Company{
		Name:        Guarantor1Name,
		LegalRep:    Guarantor1LegalRep,
		Address:     Guarantor1Address,
		Telephone:   Guarantor1Telephone,
		AccountName: Guarantor1AccountName,
		BankName:    Guarantor1BankName,
		BankAccount: Guarantor1BankAccount,
	}
}

// GetGuarantor2 获取担保公司2信息
func GetGuarantor2() Company {
	return Company{
		Name:        Guarantor2Name,
		LegalRep:    Guarantor2LegalRep,
		Address:     Guarantor2Address,
		Telephone:   Guarantor2Telephone,
		AccountName: Guarantor2AccountName,
		BankName:    Guarantor2BankName,
		BankAccount: Guarantor2BankAccount,
	}
}

// GetMicroLoan 获取小贷公司信息
func GetMicroLoan() Company {
	return Company{
		Name:        MicroLoanName,
		LegalRep:    MicroLoanLegalRep,
		Address:     MicroLoanAddress,
		Telephone:   MicroLoanTelephone,
		AccountName: MicroLoanAccountName,
		BankName:    MicroLoanBankName,
		BankAccount: MicroLoanBankAccount,
	}
}

// GetAllCompanyInfo 获取所有公司信息
func GetAllCompanyInfo() AllCompanyInfo {
	return AllCompanyInfo{
		AssetManagement: GetAssetManagement(),
		Guarantor:       GetGuarantor1(),
		MicroLoan:       GetMicroLoan(),
	}
}

// GetBorrowerWithValidation 根据用户ID获取借款人信息并验证用户名用用户id
// 参数:
//   - userID: 用户ID
//   - username: 要验证的用户名
//
// 返回值:
//   - Borrower: 借款人信息结构体
//   - error: 错误信息，包括"用户不存在"或"用户ID与用户名不匹配"
func GetBorrowerWithValidation(userID int, username string) (Borrower, error) {
	var borrower Borrower

	// 查询数据库
	userInfo, err := model.GetUserByID(int64(userID))
	if err != nil {
		log.Error("查询用户信息失败: %v\n", err)
		return borrower, errors.New("用户ID与用户名不匹配")
	}

	// 验证用户名
	if userInfo["name"].(string) != username {
		log.Info("查询用户信息:%s", userInfo["name"])
		return borrower, errors.New("用户ID与用户名不匹配")
	}

	// 填充借款人信息
	borrower = Borrower{
		Name:      userInfo["name"].(string),
		Telephone: userInfo["mobile"].(string),
		Address:   userInfo["address"].(string),
		IDCard:    userInfo["idCard"].(string),
		EmergencyContact1: EmergencyContact{
			Name:     userInfo["emergencyContact0Name"].(string),
			Phone:    userInfo["emergencyContact0Phone"].(string),
			Relation: userInfo["emergencyContact0Relation"].(string),
		},
		EmergencyContact2: EmergencyContact{
			Name:     userInfo["emergencyContact1Name"].(string),
			Phone:    userInfo["emergencyContact1Phone"].(string),
			Relation: userInfo["emergencyContact1Relation"].(string),
		},
	}
	return borrower, nil
}

// AmountToChineseUpperDetail 将金额转中文大写
// AmountToChineseUpperDetail 将金额转中文大写
func AmountToChineseUpperDetail(amount float64) string {
	if amount == 0 {
		return "零元整"
	}

	s := fmt.Sprintf("%.2f", amount)
	parts := strings.Split(s, ".")
	integerPart := parts[0]
	decimalPart := parts[1]

	digitUpper := []string{"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"}
	unitGroup := []string{"", "万", "亿", "兆"} // 扩展到兆级别
	unitSub := []string{"", "拾", "佰", "仟"}

	intLen := len(integerPart)
	groups := (intLen + 3) / 4 // 四位一组
	intResult := ""
	zeroFlag := false // 标记是否需要加“零”

	for g := 0; g < groups; g++ {
		subResult := ""
		segmentHasValue := false
		for i := 0; i < 4; i++ {
			idx := intLen - g*4 - i - 1
			if idx < 0 {
				break
			}
			d := int(integerPart[idx] - '0')
			if d == 0 {
				if segmentHasValue {
					zeroFlag = true
				}
				continue
			}
			if zeroFlag {
				subResult = "零" + subResult
				zeroFlag = false
			}
			subResult = digitUpper[d] + unitSub[i] + subResult
			segmentHasValue = true
		}
		if segmentHasValue {
			subResult += unitGroup[g]
		}
		intResult = subResult + intResult
	}

	if intResult == "" {
		intResult = digitUpper[0]
	}
	intResult += "元"

	// 处理小数部分
	decimalUnit := []string{"角", "分"}
	decResult := ""
	if decimalPart != "00" {
		if decimalPart[0] != '0' {
			decResult += digitUpper[int(decimalPart[0]-'0')] + decimalUnit[0]
		}
		if decimalPart[1] != '0' {
			decResult += digitUpper[int(decimalPart[1]-'0')] + decimalUnit[1]
		}
	} else {
		decResult = "整"
	}

	return intResult + decResult
}

// GetBankCardInfo 获取银行卡信息
func GetBankCardInfo(ctx context.Context, userID int) ([]model.BusinessBankCards, error) {
	cardService := model.NewBusinessBankCardsService(ctx)
	cards, err := cardService.GetBankCardsByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询银行卡信息失败: %v", err)
	}

	return cards, nil
}

// GetCompleteBorrowerInfo 获取完整借款人信息
func GetCompleteBorrowerInfo(ctx context.Context, userID int, username string) (Borrower, error) {
	// 获取基本借款人信息
	borrower, err := GetBorrowerWithValidation(userID, username)
	if err != nil {
		return Borrower{}, err
	}

	// 获取银行卡信息
	log.Info("获取银行卡信息")
	bankInfo, err := GetBankCardInfo(ctx, userID)
	if err != nil {
		return Borrower{}, fmt.Errorf("获取银行卡信息失败: %v", err)
	}

	// 合并银行卡信息
	borrower.BankName = bankInfo[0].BankName
	borrower.BankAccount = bankInfo[0].BankCardNo

	return borrower, nil
}

// 定义错误码
var (
	ErrParamsEmpty        = errors.New("参数错误，参数不能为空")
	ErrAuthRecordNotFound = errors.New("未查询到认证记录")
	ErrAuthPending        = errors.New("认证中，请稍后再查")
	ErrAuthFailed         = errors.New("认证失败")
)

func CheckRealNameAuth(name, idCard string) (string, error) {
	// 基本参数校验
	log.Info("开始实名认证检查")
	if name == "" || idCard == "" {
		return "", ErrParamsEmpty
	}

	// 构造请求
	request := map[string]interface{}{
		"userType": 2,
		"userName": name,
		"idNo":     idCard,
	}
	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("构造请求参数失败: %w", err)
	}

	// 调用第三方接口
	log.Info("开始调用第三方接口进行实名认证检查")
	response := service.CheckPersonalRealName(string(jsonData))
	if response == nil {
		return "", errors.New("第三方接口请求失败")
	}

	var res AuthResponse

	if err := json.Unmarshal(response, &res); err != nil {
		return "", fmt.Errorf("解析响应失败: %w", err)
	}

	// 处理错误码
	switch res.Code {
	case 100000: // 成功
		switch res.Data[0].Result {
		case 1: // 认证成功
			return res.Data[0].SerialNo, nil
		case 0: // 认证中
			return "", ErrAuthPending
		case 2: // 认证失败
			return "", ErrAuthFailed
		default:
			return "", fmt.Errorf("未知认证结果状态: %d", res.Data[0].Result)
		}
	case 100579:
		return "", ErrParamsEmpty
	case 100721:
		return "", ErrAuthRecordNotFound
	default:
		return "", fmt.Errorf("接口返回错误[%d]: %s", res.Code, res.Msg)
	}
}

func CreatePrepareContract(borrower Borrower, product *model.ProductRules, schedule *repayment.RepaymentSchedule, contractNo string) string {

	// 创建合同配置
	var createFile Config.CreateFileConfig
	createFile.ContractName = fmt.Sprintf("%s借款合同", borrower.Name)
	createFile.ContractNo = contractNo
	createFile.ValidityTime = 1                                                                                                                                                                                      // 默认1天有效期
	createFile.SignOrder = 1                                                                                                                                                                                         // 默认签署顺序为1
	createFile.RedirectUrl = fmt.Sprintf("%s://%s:%s/#/pages/Agreement/callBackContract?contract_no=%s", global.App.Config.App.H5Protocol, global.App.Config.App.Hostname, global.App.Config.App.H5port, contractNo) // 重定向地址 // 替换为实际的重定向地址

	// 添加模板信息
	var LoanNoticeTemplate Config.Templates // 贷款告知书
	LoanNoticeTemplate.TemplateNo = LOAN_NOTICE_TEMPLATE_ID

	LoanNoticeTemplate.FillData = map[string]string{
		BorrowerName_key:          borrower.Name,
		Guarantor1CompanyName_key: Guarantor1Name, // 根据业务需要填固定值或参数
		BorrowerIDCard_key:        borrower.IDCard,
		LoanAmount_key:            fmt.Sprintf("%.2f", product.LoanAmount),   // 总本金
		GuaranteeFee_key:          fmt.Sprintf("%.2f", product.GuaranteeFee), // 担保费
	}

	// 创建还款计划表
	repaymentTable := Config.TableData{
		Keyword:   TableKeyword_key, // 表格关键字
		RowValues: make([]Config.RowValue, 0),
	}

	// 添加每期数据行
	for i, period := range schedule.Periods {
		row := Config.RowValue{
			InsertRow: i > 2, // 第2行数据行不标记为新增，后续标记为新增
			ColValues: []string{
				fmt.Sprintf("第%d期", period.PeriodNumber), // 账期
				period.DueDate, // 还款时间
				fmt.Sprintf("%.2f", period.TotalDueAmount), // 当期应还总额
			},
		}
		repaymentTable.RowValues = append(repaymentTable.RowValues, row)
	}

	LoanNoticeTemplate.TableDatas = []Config.TableData{repaymentTable}

	var GuaranteeIntermediaryTemplate Config.Templates // 担保协议
	GuaranteeIntermediaryTemplate.TemplateNo = GUARANTEE_TEMPLATE_ID
	GuaranteeIntermediaryTemplate.FillData = map[string]string{
		ContractID_key:            contractNo,
		BorrowerName_key:          borrower.Name,
		BorrowerIDCard_key:        borrower.IDCard,
		BorrowerTelephone_key:     borrower.Telephone,
		Guarantor1CompanyName_key: Guarantor1Name,
		Guarantor1LegalPerson_key: Guarantor1LegalRep,
		Guarantor1Address_key:     Guarantor1Address,
		Guarantor1Tel_key:         Guarantor1Telephone,
		MicroLoanCompanyName_key:  MicroLoanName,
		LoanAmount_key:            fmt.Sprintf("%.2f", product.LoanAmount),          // 借款金额
		LoanAmountInWords_key:     AmountToChineseUpperDetail(product.LoanAmount),   // 借款金额大写
		GuaranteeFee_key:          fmt.Sprintf("%.2f", product.GuaranteeFee),        // 担保费
		GuaranteeInWordsFee_key:   AmountToChineseUpperDetail(product.GuaranteeFee), // 担保费大写
		PreGuaranteeFee_key:       fmt.Sprintf("%.2f", product.PreGuaranteeFee),     // 签订合同时当日支付担保费 = 前置担保费
		BorrowerAddress_key:       borrower.Address,
	}

	// 担保费支付计划
	guaranteeFeeTable := Config.TableData{
		Keyword:   GuaranteeFeeTable_key, // 表格关键字
		RowValues: make([]Config.RowValue, 0),
	}

	for i, period := range schedule.Periods {
		row := Config.RowValue{
			InsertRow: i > 2, // 第1行数据行不标记为新增，后续标记为新增
			ColValues: []string{
				fmt.Sprintf("第%d期", period.PeriodNumber), // 期数
				period.DueDate, // 还款时间
				fmt.Sprintf("%.2f", period.DueGuaranteeFee), // 当期应支付担保费
			},
		}
		guaranteeFeeTable.RowValues = append(guaranteeFeeTable.RowValues, row)
	}

	GuaranteeIntermediaryTemplate.TableDatas = []Config.TableData{guaranteeFeeTable}

	var LoanContractTemplate Config.Templates // 借款合同
	LoanContractTemplate.TemplateNo = DAIKUAN_TEMPLATE_ID
	LoanContractTemplate.FillData = map[string]string{
		ContractID_key:           contractNo,
		MicroLoanCompanyName_key: MicroLoanName,
		MicroLoanLegalPerson_key: MicroLoanLegalRep,
		MicroLoanAddress_key:     MicroLoanAddress,
		MicroLoanTelephone_key:   MicroLoanTelephone,
		BorrowerName_key:         borrower.Name,
		BorrowerIDCard_key:       borrower.IDCard,
		BorrowerAddress_key:      borrower.Address,
		BorrowerTelephone_key:    borrower.Telephone,

		LoanAmountInWords_key: AmountToChineseUpperDetail(product.LoanAmount),
		//ToTalDayKey:              fmt.Sprintf("%d", product.TotalPeriods*product.LoanPeriod), // 总天数 为总期数*每期天数
		BeginDate_key:                 time.Now().Format("2006-01-02"),                  // 开始日期
		EndDate_key:                   schedule.Periods[product.TotalPeriods-1].DueDate, // 结束日期
		AnnualInterestRate_key:        fmt.Sprintf("%.2f%%", product.AnnualInterestRate),
		BorrowerBankName_key:          borrower.BankName,
		BorrowerBankAccount_key:       borrower.BankAccount,
		BorrowerAccountName_key:       borrower.Name,                       // 银行账户名
		EmergencyContact1Name_key:     borrower.EmergencyContact1.Name,     // 紧急联系人1姓名
		EmergencyContact1Relation_key: borrower.EmergencyContact1.Relation, // 紧急联系人1关系
		EmergencyContact1Phone_key:    borrower.EmergencyContact1.Phone,    // 紧急联系人1联系方式
		EmergencyContact2Name_key:     borrower.EmergencyContact2.Name,     // 紧急联系人2姓名
		EmergencyContact2Relation_key: borrower.EmergencyContact2.Relation, // 紧急联系人2关系
		EmergencyContact2Phone_key:    borrower.EmergencyContact2.Phone,    // 紧急联系人2联系方式
		// AssetMgmtAccountName_key: "/",
		// AssetMgmtBankName_key:    "/",
		// AssetMgmtBankAccount_key: "/",
	}

	// var IntermediaryAgreementTemplate Config.Templates // 居间协议书
	// IntermediaryAgreementTemplate.TemplateNo = INTERMEDIARY_TEMPLATE_ID
	// IntermediaryAgreementTemplate.FillData = map[string]string{
	// 	BorrowerName_key:        borrower.Name,
	// 	BorrowerTelephone_key:   borrower.Telephone,
	// 	BorrowerIDCard_key:      borrower.IDCard,
	// 	GuaranteeFee_key:        fmt.Sprintf("%f", schedule.TotalGuaranteeFee),          // 担保费
	// 	GuaranteeInWordsFee_key: AmountToChineseUpperDetail(schedule.TotalGuaranteeFee), // 担保费大写
	// 	BorrowerBankName_key:    borrower.BankName,
	// 	BorrowerBankAccount_key: borrower.BankAccount,
	// 	BorrowerAccountName_key: borrower.Name, // 银行账户名
	// }

	var EntrustedGuaranteeTemplate Config.Templates // 承诺书
	EntrustedGuaranteeTemplate.TemplateNo = ENTRUST_GUARANTEE_TEMPLATE_ID
	EntrustedGuaranteeTemplate.FillData = map[string]string{
		BorrowerName_key:    borrower.Name,
		BorrowerAddress_key: borrower.Address,
		BorrowerIDCard_key:  borrower.IDCard,
	}

	var CreditorAssignmentNoticeTemplate Config.Templates // 债权转让通知书
	CreditorAssignmentNoticeTemplate.TemplateNo = CREDITOR_ASSIGNMENT_NOTICE_TEMPLATE_ID
	CreditorAssignmentNoticeTemplate.FillData = map[string]string{
		BorrowerName_key:         borrower.Name,                           // 借款人姓名
		MicroLoanCompanyName_key: MicroLoanName,                           // 小贷公司名称
		AssetMgmtCompanyName_key: AssetMgmtName,                           // 资管公司名称
		LoanAmount_key:           fmt.Sprintf("%.2f", product.LoanAmount), // 债权金额
		AssetMgmtAccountName_key: AssetMgmtAccountName,                    // 资管公司账户名
		AssetMgmtBankName_key:    AssetMgmtBankName,                       // 资管
		AssetMgmtBankAccount_key: AssetMgmtBankAccount,                    // 资管公司银行账号
	}

	createFile.Templates = append(createFile.Templates, LoanNoticeTemplate)
	createFile.Templates = append(createFile.Templates, GuaranteeIntermediaryTemplate)
	createFile.Templates = append(createFile.Templates, LoanContractTemplate)
	//createFile.Templates = append(createFile.Templates, IntermediaryAgreementTemplate)
	createFile.Templates = append(createFile.Templates, EntrustedGuaranteeTemplate)
	createFile.Templates = append(createFile.Templates, CreditorAssignmentNoticeTemplate)
	var createFileJson string
	if data, err := json.Marshal(createFile); err == nil {
		createFileJson = string(data)
	}
	return createFileJson
}

func addSignerinfo(borrower Borrower, ContractNo string) string {

	// === 配置个人签章 ===
	addSignerConfig := Config.AddSignerConfig{
		ContractNo:   ContractNo,
		Account:      borrower.IDCard, // 签约人的序列号,用身份证来做
		SignType:     3,               // 3：有感知签约
		ValidateType: 3,               // 3：人脸识别
	}

	// 签章位置：模板编号 AttachNo 为 1~5；LocationMode 全是 4
	for i := 1; i <= 5; i++ {
		mode := 4
		// addSignerConfig.SignStrategyList = append(addSignerConfig.SignStrategyList, Config.SignStrategy{
		// 	AttachNo:     i,
		// 	LocationMode: mode,
		// 	SignKey:      BorrowerSeal_key,
		// })
		addSignerConfig.SignStrategyList = append(addSignerConfig.SignStrategyList, Config.SignStrategy{
			AttachNo:     i,
			LocationMode: mode,
			SignKey:      BorrowerSealTime_key, // 借款人印章时间
			SignType:     2,
		})
	}

	//=== 配置担保企业2签章 ===
	// addSignerConfig2 := Config.AddSignerConfig{
	// 	ContractNo: ContractNo,
	// 	Account:    DANBAO2_ACCOUNT, // 企业认证编号
	// 	SignType:   2,               // 企业无感知签约
	// }

	// // 企业签章位置：AttachNo=1~5，LocationMode=4
	// //签约居间协议书
	// addSignerConfig2.SignStrategyList = append(addSignerConfig2.SignStrategyList, Config.SignStrategy{
	// 	AttachNo:     4,
	// 	LocationMode: 4,
	// 	SignKey:      Guarantor2Seal_key,
	// })

	//小贷公司签约
	// 签约借款合同
	addSignerConfig3 := Config.AddSignerConfig{
		ContractNo: ContractNo,
		Account:    XIAODAI_ACCOUNT, // 企业认证编号
		SignType:   2,               // 企业无感知签约
	}

	// 企业签章位置：AttachNo=1~5，LocationMode=4

	addSignerConfig3.SignStrategyList = append(addSignerConfig3.SignStrategyList, Config.SignStrategy{
		AttachNo:     3,
		LocationMode: 4,
		SignKey:      MicroLoanSeal_key, // 小贷公司印章
	})
	// 担保公司1签约
	// 签约居间协议书
	addSignerConfig4 := Config.AddSignerConfig{
		ContractNo: ContractNo,
		Account:    DANBAO1_ACCOUNT, // 企业认证编号
		SignType:   2,               // 企业无感知签约
	}

	// 企业签章位置：AttachNo=1~5，LocationMode=4

	addSignerConfig4.SignStrategyList = append(addSignerConfig4.SignStrategyList, Config.SignStrategy{
		AttachNo:     2,
		LocationMode: 4,
		SignKey:      Guarantor1Seal_key, // 担保公司1公司印章
	})
	// 资管公司签约
	// 签约债权转让书
	addSignerConfig5 := Config.AddSignerConfig{
		ContractNo: ContractNo,
		Account:    ZIGUAN_ACCOUNT, // 企业认证编号
		SignType:   2,              // 企业无感知签约
	}

	// 企业签章位置：AttachNo=1~5，LocationMode=4

	addSignerConfig5.SignStrategyList = append(addSignerConfig5.SignStrategyList, Config.SignStrategy{
		AttachNo:     5,
		LocationMode: 4,
		SignKey:      AssetMgmtSeal_key, // 资管公司印章
	})
	// === 合并配置，转 JSON ===
	//addSignerConfigList := []Config.AddSignerConfig{addSignerConfig, addSignerConfig2}
	addSignerConfigList := []Config.AddSignerConfig{addSignerConfig, addSignerConfig3, addSignerConfig4, addSignerConfig5}
	data, err := json.Marshal(addSignerConfigList)
	if err != nil {
		// 出错时返回空串或可考虑返回错误信息
		return ""
	}
	return string(data)
}

// 注册用户请求结构体
type RegisterUserRequest struct {
	Account  string `json:"account"`            // 用户唯一识别码（身份证号）
	SerialNo string `json:"serialNo,omitempty"` // 实名认证流水号
}

func RegisterUser(account string, serialNo string) error {
	// 准备注册请求参数
	registerReq := RegisterUserRequest{
		Account:  account,
		SerialNo: serialNo,
	}
	registerJson, err := json.Marshal(registerReq)
	if err != nil {
		return fmt.Errorf("序列化注册参数失败: %v", err)
	}

	// 调用注册接口
	registerRespBytes := service.CreatePersonalAccount(string(registerJson))

	// 解析返回值
	var registerResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			SealNo string `json:"sealNo"`
		} `json:"data"`
	}
	if err := json.Unmarshal(registerRespBytes, &registerResp); err != nil {
		return fmt.Errorf("解析注册响应失败: %v", err)
	}

	log.Info("注册接口返回: %+v\n", registerResp)

	// 用 switch 简化判断
	switch registerResp.Code {
	case 100000:
		return nil
	case 100021:
		return nil
	default:
		return fmt.Errorf("注册用户失败，code=%d, msg=%s", registerResp.Code, registerResp.Msg)
	}
}

type QueryContractStatusRequest struct {
	ContractNo string `json:"contractNo"`
}

type QueryContractStatusResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		ContractNo   string         `json:"contractNo"`
		Status       int            `json:"status"` // 合同状态 0：等待签约1：签约中2：已签约3：过期4：拒签6：作废7：撤销
		PageSizeMap  map[string]int `json:"pageSizeMap"`
		Remark       string         `json:"remark"`
		ContractName string         `json:"contractName"`
		ValidityTime string         `json:"validityTime"`
		PreviewUrl   string         `json:"previewUrl"`
		EmbeddedUrl  string         `json:"embeddedUrl"`
		SignUser     []SignUser_Re  `json:"signUser"`
	} `json:"data"`
}

type SignUser_Re struct {
	Account         string   `json:"account"`
	SignUrl         string   `json:"signUrl"`
	SignOrder       int      `json:"signOrder"`
	Name            string   `json:"name"`
	IDCard          string   `json:"idCard"`
	Mobile          string   `json:"mobile"`
	UserType        int      `json:"userType"`
	SignStatus      int      `json:"signStatus"`
	SignType        int      `json:"signType"`
	ValidateType    int      `json:"validateType"`
	SignMark        string   `json:"signMark"`
	ContractViewHis []string `json:"contractViewHis"`
}

func QueryContractStatus(contractNo string) (*QueryContractStatusResponse, error) {
	// 准备请求体
	req := QueryContractStatusRequest{ContractNo: contractNo}
	reqJson, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 调用第三方接口
	respBytes := service.GetContract(string(reqJson))

	// 解析返回
	var resp QueryContractStatusResponse
	err = json.Unmarshal(respBytes, &resp)
	if err != nil {
		return nil, fmt.Errorf("解析返回失败: %v", err)
	}

	// 判断返回码

	if resp.Code != 100000 {
		var errMsg string
		switch resp.Code {
		case 100056:
			errMsg = "参数错误，合同编号为空"
		case 100066:
			errMsg = "合同不存在"
		case 100205:
			errMsg = "短链接生成失败"
		case 100613:
			errMsg = "合同已删除"
		default:
			errMsg = "查询失败，错误码: " + fmt.Sprint(resp.Code)
		}
		return nil, fmt.Errorf("查询失败，返回码: %d, 消息: %s", resp.Code, errMsg)
	}
	return &resp, nil
}
