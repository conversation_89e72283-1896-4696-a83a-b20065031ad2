package risk

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"fincore/app/business/blacklist"
	"fincore/app/business/channel"
	"fincore/app/business/risk/external"
	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskmodelservice"
	"fincore/thirdparty/riskthirdparty"
	"fincore/utils/gform"
	"fincore/utils/lock"
	"fincore/utils/log"
	"fincore/utils/shopspringutils"
)

// RiskEvaluationRequest 风控评估请求
type RiskEvaluationRequest struct {
	CustomerID uint64 `json:"customer_id" binding:"required"`
}

// RiskEvaluationResponse 风控评估响应
type RiskEvaluationResponse struct {
	RiskReportID         string  `json:"risk_report_id"`           // 风控报告ID
	RiskScore            int     `json:"risk_score"`               // 风控分数
	RiskResult           int     `json:"risk_result"`              // 风控结果
	AvailableCreditLimit float64 `json:"available_credit_limit"`   // 客户可用授信额度
	EvaluationTime       string  `json:"evaluation_time"`          // 评估时间
	FailureType          *string `json:"failure_type,omitempty"`   // 失败类型
	FailureReason        *string `json:"failure_reason,omitempty"` // 失败原因
}

// LoanProductsRequest 贷款产品匹配请求
type LoanProductsRequest struct {
	CustomerID uint64 `form:"customer_id" binding:"required"`
	ChannelID  uint64 `form:"channel_id" binding:"required"`
}

// LoanProduct 贷款产品信息
type LoanProduct struct {
	ProductID          int     `json:"product_id"`
	ProductName        string  `json:"product_name"`
	LoanAmount         float64 `json:"loan_amount"`
	LoanPeriod         int     `json:"loan_period"`
	AnnualInterestRate float64 `json:"annual_interest_rate"`
	InterestRate       float64 `json:"interest_rate"`
	RepaymentMethod    string  `json:"repayment_method"`
	ChannelName        string  `json:"channel_name"`
	ChannelID          uint64  `json:"channel_id"`
	MaxAmount          float64 `json:"max_amount"`
	MinAmount          float64 `json:"min_amount"`
	Term               int     `json:"term"`
	MinRiskScore       float64 `json:"min_risk_score"`
	MaxRiskScore       float64 `json:"max_risk_score"`
}

// LoanProductsResponse 贷款产品匹配响应
type LoanProductsResponse struct {
	OverallCreditLimit   float64               `json:"overall_credit_limit"`   // 客户授信额度
	AvailableCreditLimit float64               `json:"available_credit_limit"` // 客户可用授信额度
	Products             []*model.ProductRules `json:"products"`
}

// RiskReportResponse 风控报告响应
type RiskReportResponse struct {
	EvaluationID   string                 `json:"evaluation_id"`
	RiskScore      int                    `json:"risk_score"`
	RiskResult     int                    `json:"risk_result"`
	FailureType    *string                `json:"failure_type,omitempty"`
	FailureReason  *string                `json:"failure_reason,omitempty"`
	EvaluationTime string                 `json:"evaluation_time"`
	DwfScore       *string                `json:"dwf_score,omitempty"`
	RawData        map[string]interface{} `json:"raw_data"`
}

// RiskService 风控服务
type RiskService struct {
	riskEvalService           *model.RiskEvaluationService
	productService            *model.ProductRulesService
	channelService            *channel.ChannelService
	thirdPartyService         *riskthirdparty.RiskThirdPartyService
	riskModelService          *riskmodelservice.RiskModelService
	blacklistService          *blacklist.BlacklistService
	businessAppAccountService *model.BusinessAppAccountService
	logger                    *log.Logger
}

// NewRiskService 创建风控服务实例
func NewRiskService(
	ctx context.Context,
) *RiskService {
	return &RiskService{
		riskEvalService:           model.NewRiskEvaluationService(),
		productService:            model.NewProductRulesService(),
		channelService:            channel.NewChannelService(),
		thirdPartyService:         riskthirdparty.NewRiskThirdPartyService(),
		riskModelService:          riskmodelservice.NewRiskModelService(ctx),
		blacklistService:          blacklist.NewBlacklistService(ctx),
		businessAppAccountService: model.NewBusinessAppAccountService(),
		logger:                    log.Risk().WithContext(ctx),
	}
}

// checkBlacklist 检查黑名单并返回拒绝响应（如果命中）
func (s *RiskService) checkBlacklist(ctx context.Context, customerInfo *model.BusinessAppAccount, IsInternalModel bool) *RiskEvaluationResponse {
	blacklistResult, err := s.blacklistService.CheckUserBlacklist(ctx, uint64(customerInfo.ID), customerInfo)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Uint64("customer_id", uint64(customerInfo.ID)),
			log.String("action", "blacklist_check_failed"),
		).Error("黑名单检查失败")
		return nil // 黑名单检查失败，继续后续流程
	}

	if !blacklistResult.IsBlacklisted {
		return nil // 未命中黑名单
	}

	// 命中黑名单，记录日志并返回拒绝结果
	s.logger.WithFields(
		log.Uint64("customer_id", uint64(customerInfo.ID)),
		log.String("blacklist_type", string(blacklistResult.BlacklistType)),
		log.Any("reasons", blacklistResult.Reasons),
		log.String("action", "internal_blacklist_hit"),
	).Info("用户命中内部黑名单")
	evaluationID := generateEvaluationID(int(customerInfo.ID))
	evaluationSource := model.EvaluationSourceThirdParty
	if IsInternalModel {
		evaluationID = evaluationID + "_async"
		evaluationSource = model.EvaluationSourceInternalModel
	}

	failureType := riskmodelservice.FailureTypeInternalBlacklist
	failureReason := fmt.Sprintf("%v", blacklistResult.Details)

	// 存储黑名单拒绝的评估结果
	evaluation := &model.RiskEvaluation{
		EvaluationID:     evaluationID,
		CustomerID:       int(customerInfo.ID),
		RiskScore:        model.MinRiskScore,
		RiskResult:       model.REJECTED,
		EvaluationTime:   time.Now(),
		FailureType:      &failureType,
		FailureReason:    &failureReason,
		EvaluationSource: evaluationSource,
	}

	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		s.logger.WithError(err).WithFields(
			log.String("evaluation_id", evaluationID),
			log.Uint64("customer_id", uint64(customerInfo.ID)),
			log.String("action", "store_blacklist_rejection_failed"),
		).Error("存储黑名单拒绝结果失败")
	}

	return &RiskEvaluationResponse{
		RiskReportID:   evaluationID,
		RiskScore:      model.MinRiskScore,
		RiskResult:     model.REJECTED,
		EvaluationTime: evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
		FailureType:    &failureType,
		FailureReason:  &failureReason,
	}
}

// evaluateWithOriginalModelAsync 异步调用原有风控模型
func (s *RiskService) evaluateWithOriginalModelAsync(ctx context.Context, customerInfo *model.BusinessAppAccount, thirdPartyEvaluationID string) error {
	evaluationID := thirdPartyEvaluationID + "_async"
	// 构建风控请求参数
	riskReq := &riskthirdparty.RiskRequest{
		Name:   customerInfo.Name,
		IDNo:   customerInfo.IDCard,
		Mobile: customerInfo.Mobile,
	}

	s.logger.WithFields(
		log.Uint64("customer_id", uint64(customerInfo.ID)),
		log.String("third_party_evaluation_id", thirdPartyEvaluationID),
		log.String("async_evaluation_id", evaluationID),
		log.String("action", "async_original_model_start"),
	).Info("开始异步调用原有风控模型")

	// 执行内部黑名单检查
	if blacklistResponse := s.checkBlacklist(ctx, customerInfo, true); blacklistResponse != nil {
		return nil
	}
	// 调用风控模型服务
	modelResult, err := s.riskModelService.ProcessRiskEvaluationWithCustomer(ctx, thirdPartyEvaluationID, riskReq)
	if err != nil {
		return fmt.Errorf("风控模型评估失败: %v", err)
	}

	// 存储评估结果
	evaluation := s.buildEvaluation(evaluationID, uint64(customerInfo.ID), modelResult)
	evaluation.EvaluationSource = "internal_model_async" // 标记为异步自研风控模型
	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		return fmt.Errorf("存储异步评估结果失败: %v", err)
	}

	s.logger.WithFields(
		log.Uint64("customer_id", uint64(customerInfo.ID)),
		log.String("evaluation_id", evaluationID),
		log.Int("risk_result", evaluation.RiskResult),
		log.Int("risk_score", evaluation.RiskScore),
		log.String("action", "async_original_model_completed"),
	).Info("异步原有风控模型评估完成")

	return nil
}

// evaluateRiskModel 调用风控模型并处理结果
func (s *RiskService) evaluateWithOriginalModel(ctx context.Context, db gform.IOrm, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	evaluationID := generateEvaluationID(int(customerInfo.ID))
	// 构建风控请求参数
	riskReq := &riskthirdparty.RiskRequest{
		Name:   customerInfo.Name,
		IDNo:   customerInfo.IDCard,
		Mobile: customerInfo.Mobile,
	}

	fmt.Printf("获取到的客户信息: %s %s %s\n", customerInfo.Name, customerInfo.IDCard, customerInfo.Mobile)

	// 调用风控模型服务
	modelResult, err := s.riskModelService.ProcessRiskEvaluationInternal(ctx, riskReq)
	if err != nil {
		return nil, fmt.Errorf("风控模型评估失败: %v", err)
	}

	// 存储第三方风控原始数据（非外部黑名单情况）
	if modelResult.FailureType != riskmodelservice.FailureTypeExternalBlacklist {
		if err := s.storeThirdPartyRawData(evaluationID, modelResult); err != nil {
			fmt.Printf("存储第三方原始数据失败: %v\n", err)
		}
	}

	// 存储评估结果
	evaluation := s.buildEvaluation(evaluationID, uint64(customerInfo.ID), modelResult)
	evaluation.EvaluationSource = "internal_model" // 标记为自研风控模型
	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		return nil, fmt.Errorf("存储评估结果失败: %v", err)
	}

	// 调用统一的后处理逻辑
	return s.processEvaluationResult(ctx, db, evaluation, customerInfo)
}

// buildEvaluation 构建评估结果对象
func (s *RiskService) buildEvaluation(evaluationID string, customerID uint64, modelResult *riskmodelservice.RiskEvaluationResult) *model.RiskEvaluation {
	evaluation := &model.RiskEvaluation{
		EvaluationID:     evaluationID,
		CustomerID:       int(customerID),
		RiskScore:        int(modelResult.FinalScore),
		RiskResult:       modelResult.FinalResult,
		EvaluationSource: "internal_model",
		EvaluationTime:   time.Now(),
	}
	if global.App.Config.RiskModel.RejectScore == 0 {
		global.App.Config.RiskModel.RejectScore = 435
	}
	// 设置失败类型和原因
	if modelResult.FinalResult == model.REJECTED {
		if modelResult.FailureType != "" {
			evaluation.FailureType = &modelResult.FailureType
			evaluation.FailureReason = &modelResult.FailureReason
		}
	} else if modelResult.FinalScore <= float64(global.App.Config.RiskModel.RejectScore) {
		evaluation.RiskResult = model.REJECTED
		failureType := riskmodelservice.FailureTypeRiskScore
		failureReason := "风控分数过低"
		evaluation.FailureType = &failureType
		evaluation.FailureReason = &failureReason
	} else if modelResult.FinalScore <= float64(global.App.Config.RiskModel.PassScore) {
		evaluation.RiskResult = model.REVIEW
	} else {
		evaluation.RiskResult = model.APPROVED
	}

	return evaluation
}

// processEvaluationResult 处理评估结果的后续逻辑
func (s *RiskService) processEvaluationResult(ctx context.Context, db gform.IOrm, evaluation *model.RiskEvaluation, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	maxCredit, err := s.getMaxCreditByRiskScore(ctx, customerInfo, evaluation)

	if err != nil {
		return nil, err
	}
	// 使用通用函数计算额度信息
	quotaResult := calculateQuotaInfo(customerInfo.AllQuota, customerInfo.ReminderQuota, maxCredit)

	// 处理RefreshRiskData调用时的额度提升
	allQuote, remainingQuota := s.handleRefreshRiskDataPromotion(customerInfo, evaluation.RiskResult, quotaResult)

	// 使用decimal进行精确比较，判断是否需要更新额度
	if shopspringutils.CompareAmountsWithDecimal(allQuote, customerInfo.AllQuota) != 0 || shopspringutils.CompareAmountsWithDecimal(customerInfo.ReminderQuota, remainingQuota) != 0 {
		s.logger.WithFields(
			log.Int("customer_id", evaluation.CustomerID),
			log.Float64("new_all_quota", allQuote),
			log.Float64("new_remaining_quota", remainingQuota),
			log.Int("new_risk_score", evaluation.RiskScore),
			log.String("action", "update_quota_and_risk_score"),
		).Info("更新额度和风控分数")
		if err := s.businessAppAccountService.UpdateQuotaAndRiskScoreWithTx(model.DB(), int64(evaluation.CustomerID), allQuote, remainingQuota, evaluation.RiskScore); err != nil {
			s.logger.WithError(err).WithFields(
				log.Int("customer_id", evaluation.CustomerID),
				log.Float64("new_all_quota", allQuote),
				log.Float64("new_remaining_quota", remainingQuota),
				log.Int("new_risk_score", evaluation.RiskScore),
				log.String("action", "update_quota_and_risk_score_failed"),
			).Error("更新额度和风控分数失败")
			return nil, err
		}
	}

	return &RiskEvaluationResponse{
		RiskReportID:         evaluation.EvaluationID,
		RiskScore:            evaluation.RiskScore,
		RiskResult:           evaluation.RiskResult,
		AvailableCreditLimit: remainingQuota,
		EvaluationTime:       evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
		FailureType:          evaluation.FailureType,
		FailureReason:        evaluation.FailureReason,
	}, nil
}

// evaluateWithThirdPartyService 使用第三方风控服务进行评估

func (s *RiskService) evaluateWithThirdPartyService(ctx context.Context, db gform.IOrm, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	// 创建第三方风控服务实例
	thirdService := external.NewGoDemoRiskService(ctx)

	// 调用第三方风控服务
	thirdResult, err := thirdService.EvaluateRisk(ctx, customerInfo)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Uint64("customer_id", uint64(customerInfo.ID)),
			log.String("action", "third_party_risk_service_failed"),
		).Error("第三方风控服务调用失败")
		//构造thirdResult
		thirdResult = &external.GoDemoResult{
			AuditResult:  "CallThirdRiskFailed",
			LeidaV4Data:  "",
			TanZhenCData: "",
			ZwscData:     "",
			CreatedAt:    time.Now(),
		}
	}

	// 生成评估ID
	evaluationID := generateEvaluationID(int(customerInfo.ID))

	// 映射风控结果
	riskResult, riskScore, failureReason := thirdService.MapToRiskResult(thirdResult)

	// 存储原始数据到risk_raw_data表
	if err := s.storeThirdPartyRawDataFromExternal(evaluationID, thirdResult); err != nil {
		s.logger.WithError(err).WithFields(
			log.String("evaluation_id", evaluationID),
			log.Uint64("customer_id", uint64(customerInfo.ID)),
			log.String("action", "store_third_party_raw_data_failed"),
		).Error("存储第三方风控原始数据失败")
		// 不阻断流程，继续执行
	}

	// 构建评估结果
	evaluation := &model.RiskEvaluation{
		EvaluationID:     evaluationID,
		CustomerID:       int(customerInfo.ID),
		RiskScore:        riskScore,
		RiskResult:       riskResult,
		EvaluationSource: model.EvaluationSourceThirdParty, // 标记为第三方风控服务
		EvaluationTime:   time.Now(),
		DwfScore:         &thirdResult.DwfScore,
	}

	// 如果有失败原因，设置失败信息
	if failureReason != "" {
		failureType := riskmodelservice.FailureTypeThirdPartyReject
		evaluation.FailureType = &failureType
		evaluation.FailureReason = &failureReason
	}

	// 存储评估结果到risk_evaluations表
	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		s.logger.WithError(err).WithFields(
			log.String("evaluation_id", evaluationID),
			log.Uint64("customer_id", uint64(customerInfo.ID)),
			log.String("action", "store_third_party_evaluation_failed"),
		).Error("存储第三方风控评估结果失败")
		return nil, fmt.Errorf("存储评估结果失败: %v", err)
	}

	// 第三方风控评估完成后，异步调用原有风控模型 todo
	go func() {
		ctxAsync := context.Background() // 使用新的context避免超时
		if err := s.evaluateWithOriginalModelAsync(ctxAsync, customerInfo, evaluationID); err != nil {
			s.logger.WithError(err).WithFields(
				log.String("evaluation_id", evaluationID),
				log.Uint64("customer_id", uint64(customerInfo.ID)),
				log.String("action", "async_original_model_failed"),
			).Error("异步调用原有风控模型失败")
		}
	}()

	// 调用统一的后处理逻辑
	return s.processEvaluationResult(ctx, db, evaluation, customerInfo)
}

// storeThirdPartyRawDataFromExternal 存储第三方风控原始数据
func (s *RiskService) storeThirdPartyRawDataFromExternal(evaluationID string, result *external.GoDemoResult) error {
	// 构建原始数据
	rawData := &model.RiskRawData{
		EvaluationID:     evaluationID,
		LeidaV4Response:  result.LeidaV4Data,
		TanZhenCResponse: result.TanZhenCData,
		ZwscResponse:     result.ZwscData,
		DataSource:       "third_party",
		CreatedAt:        result.CreatedAt,
		UpdatedAt:        result.CreatedAt,
	}

	// 存储到数据库
	return s.riskEvalService.CreateRawData(rawData)
}

// EvaluateRisk 执行基础风控评估（仅通过customerID）
func (s *RiskService) EvaluateRisk(ctx context.Context, db gform.IOrm, customerID int64) (*RiskEvaluationResponse, error) {
	customerInfo, err := s.GetCustomerInfo(ctx, customerID)
	if err != nil {
		return nil, err
	}
	// 验证必要字段
	if customerInfo.Name == "" || customerInfo.IDCard == "" || customerInfo.Mobile == "" {
		return nil, fmt.Errorf("客户信息不完整: 姓名、身份证号或手机号为空")
	}

	// 2. 查询数据库中的最新评估记录
	riskEvalService := model.NewRiskEvaluationService()
	latestEvaluation, err := riskEvalService.GetLatestEvaluationByCustomerID(int(customerID))
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Int64("customer_id", customerID),
			log.Uint64("channel_id", uint64(customerInfo.ChannelID)),
			log.String("action", "query_latest_evaluation_failed"),
		).Error("查询最新评估记录失败")
	}

	// 声明返回变量
	var rs *RiskEvaluationResponse

	// 如果有有效的评估记录，直接使用
	if err == nil && latestEvaluation != nil {
		rs = &RiskEvaluationResponse{
			RiskReportID:   latestEvaluation.EvaluationID,
			RiskScore:      latestEvaluation.RiskScore,
			RiskResult:     latestEvaluation.RiskResult,
			EvaluationTime: latestEvaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
			FailureType:    latestEvaluation.FailureType,
		}
	} else {
		// 没有评估记录，执行重新评估
		rs, err = s.PerformNewEvaluation(ctx, db, customerInfo)
		if err != nil {
			return nil, err
		}
	}

	// 后续修改结果都要执行
	if err == nil {
		// 创建一个临时的evaluation对象用于调用adjustRiskResult
		tempEvaluation := &model.RiskEvaluation{
			RiskResult: rs.RiskResult,
			RiskScore:  rs.RiskScore,
		}
		riskResult, _ := adjustRiskResultAndScore(customerInfo, tempEvaluation, true)
		rs.RiskResult = riskResult
	}

	return rs, err
}

// PerformNewEvaluation 执行新的风控评估
func (s *RiskService) PerformNewEvaluation(ctx context.Context, db gform.IOrm, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	// 获取用户锁，确保同一用户查询到结果唯一
	key := fmt.Sprintf("user_risk_lock_%d", customerInfo.ID)
	riskLock := lock.GetLock(key)
	riskLock.Lock()
	s.logger.WithFields(
		log.Int64("customer_id", int64(customerInfo.ID)),
		log.String("lock_key", key),
		log.String("action", "risk_evaluation_lock_acquired"),
	).Info("获取用户风险评估加锁成功")
	defer riskLock.Unlock()

	// 读取数据库最新评估记录
	latestEvaluation, err := s.riskEvalService.GetLatestEvaluationByCustomerID(int(customerInfo.ID))
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Int64("customer_id", int64(customerInfo.ID)),
			log.Uint64("channel_id", uint64(customerInfo.ChannelID)),
			log.String("action", "query_latest_evaluation_in_lock_failed"),
		).Error("查询最新评估记录失败")
	}

	// 如果有有效的评估记录，直接返回
	if err == nil && latestEvaluation != nil && isEvaluationValid(latestEvaluation.EvaluationTime) {
		maxCredit, err := s.getMaxCreditByRiskScore(ctx, customerInfo, latestEvaluation)
		if err != nil {
			return nil, err
		}
		//这里如果是赋予的额度，不更新
		if customerInfo.AvailableProductID == model.NoChange {
			// 使用通用函数计算额度信息
			quotaResult := calculateQuotaInfo(customerInfo.AllQuota, customerInfo.ReminderQuota, maxCredit)
			// 处理RefreshRiskData调用时的额度提升
			allQuote, remainingQuota := s.handleRefreshRiskDataPromotion(customerInfo, latestEvaluation.RiskResult, quotaResult)
			// 使用decimal进行精确比较，判断是否需要更新额度
			if shopspringutils.CompareAmountsWithDecimal(allQuote, customerInfo.AllQuota) != 0 || shopspringutils.CompareAmountsWithDecimal(customerInfo.ReminderQuota, remainingQuota) != 0 {
				s.logger.WithFields(
					log.Int("customer_id", latestEvaluation.CustomerID),
					log.Float64("new_all_quota", allQuote),
					log.Float64("new_remaining_quota", remainingQuota),
					log.Int("new_risk_score", latestEvaluation.RiskScore),
					log.String("action", "update_quota_from_cache"),
				).Info("更新额度和风险分数")
				if err := s.businessAppAccountService.UpdateQuotaAndRiskScoreWithTx(model.DB(), int64(latestEvaluation.CustomerID), allQuote, remainingQuota, latestEvaluation.RiskScore); err != nil {
					s.logger.WithError(err).WithFields(
						log.Int("customer_id", latestEvaluation.CustomerID),
						log.Float64("new_all_quota", allQuote),
						log.Float64("new_remaining_quota", remainingQuota),
						log.Int("new_risk_score", latestEvaluation.RiskScore),
						log.String("action", "update_quota_from_cache_failed"),
					).Error("更新额度和风险分数失败")
					return nil, err
				}
				customerInfo.AllQuota = allQuote
				customerInfo.ReminderQuota = remainingQuota
			}

		}
		return &RiskEvaluationResponse{
			RiskReportID:   latestEvaluation.EvaluationID,
			RiskScore:      latestEvaluation.RiskScore,
			RiskResult:     latestEvaluation.RiskResult,
			EvaluationTime: latestEvaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
			FailureType:    latestEvaluation.FailureType,
		}, nil
	}
	// 执行新的风控评估
	return s.ForceRefreshRisk(ctx, customerInfo)
}

// 强制刷新风控
func (s *RiskService) ForceRefreshRisk(ctx context.Context, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	// 检查渠道id等于0
	riskResponse := s.checkChannelIDEqualZero(ctx, customerInfo)
	if riskResponse != nil {
		return riskResponse, nil
	}
	// 检查用户状态是否为2 是否是手动拉黑黑名单
	blacklistResponse := s.checkBlacklistManually(ctx, customerInfo)
	if blacklistResponse != nil {
		return blacklistResponse, nil
	}

	// 1. 获取第三方风控配置
	thirdRiskConfig, err := external.GetThirdRiskConfig()
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Int64("customer_id", int64(customerInfo.ID)),
			log.String("action", "get_third_party_config_failed"),
		).Error("获取第三方风控配置失败")
		// 配置获取失败时使用原有风控模型
		thirdRiskConfig = nil
	}
	// 2. 执行风控评估
	if thirdRiskConfig != nil && thirdRiskConfig.Enabled {
		if blacklistResponse := s.checkBlacklist(ctx, customerInfo, false); blacklistResponse != nil {
			return blacklistResponse, nil
		}
		// 使用第三方风控服务
		return s.evaluateWithThirdPartyService(ctx, model.DB(), customerInfo)
	} else {
		// 执行黑名单检查
		if blacklistResponse := s.checkBlacklist(ctx, customerInfo, true); blacklistResponse != nil {
			return blacklistResponse, nil
		}
		// 使用原有风控模型
		return s.evaluateWithOriginalModel(ctx, model.DB(), customerInfo)
	}
}

type EvaluateRiskWithProductParams struct {
	CustomerID uint64 `json:"customer_id" binding:"required"` // 客户ID
	ProductID  uint64 `json:"product_id" binding:"required"`  // 产品ID
	ChannelID  uint64 `json:"channel_id" binding:"required"`  // 渠道ID
}

// EvaluateRiskWithProduct 执行风控评估并匹配产品（通过customerID、productID和channelID）
func (s *RiskService) EvaluateRiskWithProduct(ctx context.Context, params EvaluateRiskWithProductParams) (*RiskEvaluationResponse, error) {
	return s.EvaluateRiskWithProductTx(ctx, model.DB(), params)
}

// evaluateRiskWithProductInternal 执行风控评估并匹配产品的内部实现
func (s *RiskService) EvaluateRiskWithProductTx(ctx context.Context, db gform.IOrm, params EvaluateRiskWithProductParams) (*RiskEvaluationResponse, error) {
	// 获取客户信息
	customerInfo, err := s.GetCustomerInfo(ctx, int64(params.CustomerID))
	if err != nil {
		return nil, err
	}
	// 执行风控评估（包含获取最新记录和检查逻辑）
	baseEvaluation, err := s.PerformNewEvaluation(ctx, db, customerInfo)
	if err != nil {
		return nil, err
	}

	return s.processProductEvaluation(ctx, db, params, baseEvaluation, customerInfo)
}

// GetProductsByAmount 根据额度获取匹配的产品列表
func (s *RiskService) GetProductsByAmount(ctx context.Context, loanAmount float64) ([]*model.ProductRules, error) {
	s.logger.WithFields(
		log.Float64("loan_amount", loanAmount),
		log.String("action", "get_products_by_amount_start"),
	).Info("开始根据额度获取产品列表")

	// 1. 直接从产品表获取所有产品
	productRulesService := &model.ProductRulesService{}
	allProducts, err := productRulesService.GetActiveProductRules()
	if err != nil {
		return nil, fmt.Errorf("获取产品列表失败: %v", err)
	}

	// 2. 根据额度过滤产品规则
	var matchedProducts []*model.ProductRules
	for _, product := range allProducts {
		s.logger.WithFields(
			log.Int("product_id", product.ID),
			log.String("rule_name", product.RuleName),
			log.Float64("product_amount", product.LoanAmount),
			log.Float64("request_amount", loanAmount),
			log.String("action", "check_product_amount_match"),
		).Debug("检查产品额度匹配")

		// 额度匹配：产品额度小于等于请求额度
		if product.LoanAmount <= loanAmount {
			matchedProducts = append(matchedProducts, &product)
			s.logger.WithFields(
				log.Int("product_id", product.ID),
				log.String("rule_name", product.RuleName),
				log.String("action", "product_amount_match_success"),
			).Info("产品额度匹配成功")
		}
	}

	s.logger.WithFields(
		log.Int("count", len(matchedProducts)),
		log.Float64("loan_amount", loanAmount),
		log.String("action", "get_products_by_amount_success"),
	).Info("成功获取匹配的产品规则")
	return matchedProducts, nil
}

// GetLoanProducts 获取贷款产品列表
func (s *RiskService) GetLoanProducts(ctx context.Context, customerID int64) (*LoanProductsResponse, error) {
	// 1. 参数验证
	if customerID == 0 {
		return nil, fmt.Errorf("客户ID不能为空")
	}

	// 2. 获取客户信息
	customerInfo, err := s.GetCustomerInfo(ctx, customerID)
	if err != nil {
		return nil, fmt.Errorf("获取客户信息失败: %v", err)
	}

	// 获取用户锁，确保同一用户查询到结果唯一
	key := fmt.Sprintf("user_loan_lock_%d", customerID)
	loanLock := lock.GetLock(key).Lock()
	defer loanLock.Unlock()

	// 查询风险评估
	riskEvaluation, err := s.EvaluateRisk(ctx, model.DB(), customerID)
	if err != nil {
		return nil, err
	}

	// 3. 检查风控结果
	if riskEvaluation.RiskResult == model.REJECTED {
		response := &LoanProductsResponse{
			OverallCreditLimit: 0,
			Products:           []*model.ProductRules{},
		}
		return response, nil
	}

	// 4. 获取可用产品（不限制风控分数）
	products, err := s.getAvailableProductsForCustomer(ctx, customerInfo, nil)
	if err != nil {
		return nil, err
	}

	// 5. 过滤掉超出可用额度的产品
	filteredProducts, availableCreditLimit := s.filterProductsByQuota(products, customerInfo.ReminderQuota)

	return &LoanProductsResponse{
		OverallCreditLimit:   customerInfo.AllQuota,
		AvailableCreditLimit: availableCreditLimit,
		Products:             filteredProducts,
	}, nil
}

// GetRiskReports 获取风控报告列表（支持时间查询）
func (s *RiskService) GetRiskReports(ctx context.Context, data map[string]interface{}) ([]RiskReportResponse, error) {
	// 1. 参数转换
	customerID, err := GetCustomerIDFromData(data)
	if err != nil {
		return nil, err
	}

	// 2. 获取时间参数
	startDate := ""
	endDate := ""
	if val, ok := data["start_date"]; ok {
		if str, ok := val.(string); ok {
			startDate = str
		}
	}
	if val, ok := data["end_date"]; ok {
		if str, ok := val.(string); ok {
			endDate = str
		}
	}

	// 3. 获取风控评估记录
	evaluations, err := s.riskEvalService.GetEvaluationsByCustomerIDAndDateRange(int(customerID), startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("未找到风控评估记录: %v", err)
	}

	if len(evaluations) == 0 {
		return []RiskReportResponse{}, nil
	}

	// 5. 构建响应数据
	var reports []RiskReportResponse
	for _, evaluation := range evaluations {
		// 处理EvaluationID，去掉可能的_async后缀
		evaluationID := evaluation.EvaluationID
		if len(evaluationID) > 6 && evaluationID[len(evaluationID)-6:] == "_async" {
			evaluationID = evaluationID[:len(evaluationID)-6]
		}
		// 获取原始数据
		rawData, err := s.riskEvalService.GetRawDataByEvaluationID(evaluationID)
		rawDataMap := make(map[string]interface{})

		if err == nil {
			// 解析雷达V4数据
			if rawData.LeidaV4Response != "" {
				var leidaData map[string]interface{}
				if err := json.Unmarshal([]byte(rawData.LeidaV4Response), &leidaData); err == nil {
					rawDataMap["leida_v4"] = leidaData
				}
			}

			// 解析探真C数据
			if rawData.TanZhenCResponse != "" {
				var tanZhenData map[string]interface{}
				if err := json.Unmarshal([]byte(rawData.TanZhenCResponse), &tanZhenData); err == nil {
					rawDataMap["tan_zhen_c"] = tanZhenData
				}
			}

			// 解析中网数据
			if rawData.ZwscResponse != "" {
				var zwscData map[string]interface{}
				if err := json.Unmarshal([]byte(rawData.ZwscResponse), &zwscData); err == nil {
					rawDataMap["zwsc"] = zwscData
				}
			}
		}

		report := RiskReportResponse{
			EvaluationID: evaluation.EvaluationID,
			RiskScore:    evaluation.RiskScore,
			RiskResult:   evaluation.RiskResult,
			FailureType: func() *string {
				if evaluation.FailureType != nil {
					desc := riskmodelservice.GetFailureTypeDescription(*evaluation.FailureType)
					return &desc
				}
				return nil
			}(),
			FailureReason: func() *string {
				if evaluation.FailureReason != nil {
					desc := *evaluation.FailureReason
					return &desc
				}
				return nil
			}(),
			EvaluationTime: evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
			RawData:        rawDataMap,
			DwfScore: func() *string {
				if evaluation.DwfScore != nil {
					desc := *evaluation.DwfScore
					return &desc
				}
				return nil
			}(),
		}
		reports = append(reports, report)
	}

	return reports, nil
}

// processProductEvaluation 处理产品评估的通用逻辑
// 如果风控结果被拒绝，直接返回
func (s *RiskService) processProductEvaluation(ctx context.Context, db gform.IOrm, params EvaluateRiskWithProductParams, baseEvaluation *RiskEvaluationResponse, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	// 创建临时evaluation对象用于调用adjustRiskResult
	tempEvaluation := &model.RiskEvaluation{
		RiskResult: baseEvaluation.RiskResult,
		RiskScore:  baseEvaluation.RiskScore,
	}
	riskResult, _ := adjustRiskResultAndScore(customerInfo, tempEvaluation, false)
	s.logger.WithFields(
		log.Uint64("customer_id", params.CustomerID),
		log.Int("risk_result", riskResult),
		log.String("action", "adjust_risk_result"),
	).Info("调整风控结果")

	baseEvaluation.RiskResult = riskResult

	if baseEvaluation.RiskResult == model.REJECTED {
		return baseEvaluation, nil
	}

	products, err := s.getAvailableProductsForCustomer(ctx, customerInfo, nil)
	if err != nil {
		return nil, fmt.Errorf("获取产品匹配失败: %v", err)
	}

	// 检查指定的产品是否在匹配列表中
	matchedProduct := findProductByID(products, int(params.ProductID))
	if matchedProduct == nil {
		return nil, fmt.Errorf("指定的产品ID %d 不符合当前风险评估条件", params.ProductID)
	}

	currentAvailableQuota := customerInfo.ReminderQuota

	// 使用decimal进行精确比较，检查产品额度是否超过可用额度
	if shopspringutils.CompareAmountsWithDecimal(currentAvailableQuota, matchedProduct.LoanAmount) < 0 {
		baseEvaluation.RiskResult = model.REJECTED
		failureType := "insufficient_credit_limit"
		failureReason := fmt.Sprintf("可用额度不足，需要%.2f，可用%.2f", matchedProduct.LoanAmount, currentAvailableQuota)
		baseEvaluation.FailureType = &failureType
		baseEvaluation.FailureReason = &failureReason
		s.logger.WithFields(
			log.Uint64("customer_id", params.CustomerID),
			log.Float64("available_credit_limit", currentAvailableQuota),
			log.Float64("matched_product_loan_amount", matchedProduct.LoanAmount),
			log.String("action", "insufficient_credit_limit"),
		).Info("产品额度超过可用额度，拒绝放款")
		return baseEvaluation, nil
	}

	// 使用decimal计算扣除产品额度后的新剩余额度
	newReminderQuota := shopspringutils.SubtractAmountsWithDecimal(currentAvailableQuota, matchedProduct.LoanAmount)

	// 确定要更新的availableProductID值
	availableProductID := model.NoChange
	if customerInfo.AvailableProductID != model.NoChange {
		availableProductID = model.NoChange // 恢复用户的可用产品的id为不可用
	}

	s.logger.WithFields(
		log.Int64("customer_id", int64(params.CustomerID)),
		log.Float64("current_reminder_quota", customerInfo.ReminderQuota),
		log.Float64("new_reminder_quota", newReminderQuota),
		log.Float64("matched_product_loan_amount", matchedProduct.LoanAmount),
		log.Int("available_product_id", availableProductID),
		log.String("action", "update_reminder_quota_and_available_product_id"),
	).Info("更新用户剩余额度和可用产品ID")

	if err := s.businessAppAccountService.UpdateReminderQuotaAndAvailableProductIDWithTx(model.DB(), int64(params.CustomerID), newReminderQuota, availableProductID); err != nil {
		s.logger.WithError(err).WithFields(
			log.Int64("customer_id", int64(params.CustomerID)),
			log.Float64("new_reminder_quota", newReminderQuota),
			log.Int("available_product_id", availableProductID),
			log.String("action", "update_reminder_quota_and_available_product_id_failed"),
		).Error("更新剩余额度和可用产品ID失败")
		return nil, err
	}

	s.logger.WithFields(
		log.Uint64("customer_id", params.CustomerID),
		log.Float64("old_reminder_quota", customerInfo.ReminderQuota),
		log.Float64("new_reminder_quota", newReminderQuota),
		log.Int("available_product_id", availableProductID),
		log.String("action", "update_reminder_quota_and_available_product_id_success"),
	).Info("更新用户剩余额度和可用产品ID成功")

	// 设置可用额度并返回结果
	baseEvaluation.AvailableCreditLimit = newReminderQuota
	return baseEvaluation, nil
}

// storeThirdPartyRawData 存储第三方风控原始数据
func (s *RiskService) storeThirdPartyRawData(evaluationID string, modelResult *riskmodelservice.RiskEvaluationResult) error {
	rawData := &model.RiskRawData{
		EvaluationID: evaluationID,
		DataSource:   "xiamengbangzu", // 合并数据源
	}

	// 存储tan_zhen_c原始数据
	if tanZhenData, exists := modelResult.ThirdPartyResults["tanZhen"]; exists {
		tanZhenJSON, _ := json.Marshal(tanZhenData)
		rawData.TanZhenCResponse = string(tanZhenJSON)
	}

	// 存储leida_v4原始数据
	if leidaData, exists := modelResult.ThirdPartyResults["leida"]; exists {
		leidaJSON, _ := json.Marshal(leidaData)
		rawData.LeidaV4Response = string(leidaJSON)
	}

	// 只有当至少有一个数据源有数据时才存储
	if rawData.TanZhenCResponse != "" || rawData.LeidaV4Response != "" {
		err := s.riskEvalService.CreateRawData(rawData)
		if err != nil {
			return fmt.Errorf("存储第三方原始数据失败: %v", err)
		}
	}

	return nil
}
