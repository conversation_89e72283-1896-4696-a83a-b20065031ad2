# 配置文件已从OSS下载并解密
# 生成时间: 2025-08-29 11:36:31

dbconf:
  driver: mysql
  hostname: rm-f8zt37zczb592y476.rwlb.rds.aliyuncs.com
  hostport: "3306"
  username: fincore
  password: fincore123!@#
  database: fincore_yj
  prefix: ""
app:
  port: "8108"
  hostname: www.tsfincore.com
  h5port: "443"
  h5protocol: https
  version: 1.3.0
  env: pro
  apisecret: fincore@kk888
  allowurl: https://www.tsfincore.com,https://tsfincore.com,https://www.tsfincore.com:8080,https://tsfincore.com:8080
  tokenouttime: "120"
  clientTokenOutTime: ""
  cpunum: "3"
  domain: ""
  vueobjroot: D:/Project/develop/vue/gofly_enterprise/business
  companyPrivateHouse: ""
  rootview: webbusiness
  runlogtype: debug
  noVerifyTokenRoot: resource,webbusiness,webadmin
  noVerifyAPIRoot: resource,webbusiness,webadmin,uniapp
  noVerifyToken: /common/uploadfile/get_image,/common/install/index,/common/install/save,/admin/user/login,/admin/user/logout,/admin/user/refreshtoken,/admin/user/get_code,/admin/user/resetPassword,/business/user/login,/business/user/logout,/business/user/refreshtoken,/business/user/get_code,/business/user/resetPassword,/admin/user/get_logininfo,/business/user/get_logininfo,/uniapp/user/loginBySms,/business/captcha/getCaptcha,/business/user/postSms,/uniapp/captcha/getCaptcha,/uniapp/user/postSms,/uniapp/user/postBySms,/uniapp/user/postBySms,/uniapp/user/getUserInfo,/business/payment/manager/disbursementCallback,/business/payment/manager/PaymentCallback
  noVerifyAPI: /common/install/index,/common/install/save,/business/payment/manager/disbursementCallback
  invitationPage: /#/pages/login/login
  fileServer: http://192.168.0.20:8108
  appName: ""
  queryServer: ""
  leidaPrice: 0
  tanzhencPrice: 0
  platform: ""
jwt:
  secret: 3Bde3BGEbYqtqyEUzW3ry8jKFcaPH17fRmTmqE7MDr05Lwj978ruRKrrkb44TJ4s
  jwt_ttl: 43200
log:
  level: info
  root_dir: ./log
  format: json
  output: file
  timezone: Asia/Shanghai
  show_line: true
  max_backups: 10
  max_size: 100
  max_age: 30
  compress: true
sms:
  code_account: dh36925
  code_password: 7f372dbc69aa4b7582cc7a13329509fc
  business_account: dh36926
  business_password: 4904bdcfa78c951709ef17470863d0ac
  sign: 【江西途顺网络科技】
  apiurl: http://www.dh3t.com/json/sms/BatchSubmit
  isused: T
vbank:
  apikey: ""
  apiurl: http://v.juhe.cn/verifybankcard4/query
aiqian_envs:
  app_id: "*********"
  pro_host: https://oapi.asign.cn
  privateKey: MIIEwAIBADANBgkqhkiG9w0BAQEFAASCBKowggSmAgEAAoIBAQC2uFczQgnjOnYRATAG84UV6ZTojzj7/BCnKMqCIlFWYd8WFrmqpOXVmZLRDUjNk33YHPGwEA7l/dc/RA5p2pmNMAHgJVhZbcTV6xyjeU6LDkjyev5XJykruwxS5Y2+EyL4i/pcG6o/A+pI77H7fWNOEPini7GxgjD4bZXv8rJjwYOxE6gcwtnXb7aauRX8ezI62k7g2OUvXRYiZy6oRZoPABWSn/zEN+q3gfGpOFYtozuaUNe/+b1S/sMnts7v11rsgN0UDf/u+DDUVFe924lmeSW21r90oYQyM6XXb8SY2AReu+u8oCGwuIu02G1GlEyTSUjAONEKCGxwqGzZvw/PAgMBAAECggEBAKXSXbCy+e4xm/yKq19jmR/tv6necMSeWS6aok2/fzl50M9nCFCJHdvfZ5I5EB0hAVAj1GMH771hxPoxdTMzo66yJsGYorlmGQBaQr9I07L239TPMgs+CusY8XI5yYz6KP6PakI1CSfvEavnfArUHE84r7C94iFKGc8bBLuh8ar84u2y23HgmIv9I3Zss0quqoarCd6PNI/tyw7NaDpFPUmUOWti3PsgEQhgVo2DCM3WPHPfylPXQ43hZEudkho+HzIE4ztpYXHUOWGLTF+Q5nInNh1MgqxGcBuJuPAWomUk7prSMCav9Kht6Rflnmcw+HoPmWse9zr9ICdECgiygmECgYEA+wjxufMh1p4obHWvTxQpPZLCGsPF8g+WbMDVmmVA3uM6BHMfs3VcFetN1AWu879SLInC5uYC4UC+josAHpfObFEx6pzxja4Zn1Pshp+ArTBFZ/u9jmvvOXSYUoFuOVIZslaX8Ki0/N20ym5RvdFaFx0WwqYBZMjltn5XsT1WDS0CgYEAulWAFdg+b0Z6B9OZ13kSjrEO0WbGhu+gh8Dy8hQqJUjdLSvYMebLY2eHmxL5xG0vN7ARteJ62RZ7bDgFObyQbIOidigz7TiHeId++5SlzBnFCNRPX6eAZTZBfCq90ZveCQhcZ8hIni957RXF5XtBi3Kdqur9EHHgHnPE9ZyqhmsCgYEA0fv1V3oNAB1j6vW2IwvWQ28Tdpf0aDqptWbIRlIUJV0lFrvF9LNix+MAQy5N3g5XinHh2orkNc+Wll2nR+/r96cjfgCx/bV4MVJeM24QkM4kAIsPUKbwgLsK/1jM/p2yaP8OMXytiCdcJ0iIj6MjHNp0Q3XhDJEPtcuRRuzrojECgYEAia8n5/xTlhGzlhjrMmaKKdn3IxAYXhiuu+D9I5d21PoURI6DP8xUOW2ErDfHSzeKjlGRpJ5nPAX6ySpT4ifNaAGUiE6IoB8HKy6jy+443KmmCDIpPHseyqrelItYm4va8z20WhOKZSibpW5TPpBnDE1y55qfyAj9HENbJEnRT2UCgYEAxJhATzaX7KSd6FuHlE7byQTMSTSarPjVchAWH8exIIVFKO8SELP1OBibSd1Obuaeh/LxmTr+CPVGOWYAyu3KqT0D5QXEDnd0Wb16Rjfjudi4cogmay0MsxzM5SK76nV3G+HUvXaKZrtqg1zq5D4sPSRFbXhIZinTshmK6ysRxcU=
aiqian_sign:
  sign_address: 柳州市柳城县
  templates:
    loan_notice_template_no: **********************************
    loan_contract_template_no: **********************************
    guarantee_template_no: TNE462B6A508194CE094BB2DBB414A44DB
    entrust_guarantee_template_no: TN0F9F4D87D35244659F318E07DD597653
    creditor_assignment_notice_template_no: TNC7A826CED4484113BE7472B87437CBD5
  aiqian_merchants:
    small_loan_merchant:
      mer_name: 柳城县同盛小额贷款有限公司
      account_name: /
      bank_account: /
      bank_name: /
      legal_rep: 朱伟鸣
      address: 广西壮族自治区柳州市柳城县大埔镇白阳中路94号
      telephone: /
      sign_seal_account: ASIGN91450222MA5K9QEA5Q
    guarantor_merchant:
      mer_name: 武汉盛唐融资担保有限公司
      account_name: /
      bank_account: /
      bank_name: /
      legal_rep: /
      address: /
      telephone: /
      sign_seal_account: ASIGN91420103796300796R
    asset_merchant:
      mer_name: 南昌元界文化投资有限公司
      account_name: 南昌元界文化投资有限公司
      bank_account: "1502015009000052002"
      bank_name: 中国工商银行股份有限公司南昌航空城支行
      legal_rep: 吴国侦
      address: 江西省南昌市红谷滩区新里梵顿公馆商业区2#酒店613A室(第6层)
      telephone: /
      sign_seal_account: ASIGN91360125MAEKQKYX3C
sumpay:
  environment: prod
  test_url: https://test.sumpay.cn/entrance/gateway.htm
  prod_url: https://entrance.sumpay.cn/gateway.htm
  skip_tls_verify: true
  request_timeout: 30
  retry_count: 3
  retry_wait_time: 5
  cert:
    public_key_path: resource/cert/prod/TTFPublicKey.cer
  asset_merchant:
    mer_name: 武汉市几何资产投资管理有限公司
    mer_no: "************"
    app_id: "************"
    passwd: jihe1234
    pfx_path: resource/cert/prod/jihezg-************.pfx
  guarantee_merchant:
    mer_name: 武汉盛唐融资担保有限公司
    mer_no: "************"
    app_id: "************"
    passwd: st123
    pfx_path: resource/cert/prod/stdb-************.pfx
  small_loan_merchant:
    mer_name: 南宁市金沙小额贷款有限公司
    mer_no: "************"
    app_id: "************"
    passwd: js123
    pfx_path: resource/cert/prod/jsxd-************.pfx
risk_thirdparty:
  merchant_id: fda6167a-75bc-4feb-987f-179d8d7eed36
  aes_key: 2ae38f3a9f71141c
  url: http://*************/api/risk/interface
  product_id: leida_v4
  timeout: 45
risk_model:
  base_url: http://************:5000
  timeout: 45
  reject_score: 435
  pass_score: 621
third_risk:
  enabled: true
  url: http://riskopenapi.eenz.cn/
  app_id: KH202507241277780636309716992
  aes_key: vIGit3Mxy1+ZA3/RiA8YpQ==
  timeout: 30
  service_code: risk_queryV3
  data_source: third_party
risk_evaluation:
  evaluation_expiry_hours: 24
system_deduct:
  max_fail_count: 15
redis:
  host: ************
  port: "6379"
  password: ""
  db: 0
  timeout: 15
oss:
  endpoint: oss-cn-heyuan.aliyuncs.com
  accessKeyId: LTAI5tKkn1BJibvrspJz3mwC
  accessKeySecret: ******************************
  bucketName: fincore
  basePath: uploads/
  domain: ""
  enabled: true
