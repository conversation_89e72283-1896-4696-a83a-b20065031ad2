package risk

import (
	"context"
	"reflect"
	"time"

	"fincore/app/business/risk"
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

// RiskController 风控控制器
type RiskController struct{}

func init() {
	// 注册路由到自动路由系统
	gf.Register(&RiskController{}, reflect.TypeOf(RiskController{}).PkgPath())
}

// GetEvaluate 授信评估接口
// 路由: GET /uniapp/risk/riskcontroller/getEvaluate
func (rc *RiskController) GetEvaluate(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
		"channel_id":  c.Query("channel_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := risk.GetRiskEvaluationSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 直接进行风控评估
	customerID, err := risk.GetCustomerIDFromData(validationResult.Data)
	if err != nil {
		results.Failed(c, "参数错误", err.Error())
		return
	}
	result, err := risk.NewRiskService(ctx).EvaluateRisk(ctx, model.DB(), int64(customerID))
	if err != nil {
		results.Failed(c, "风控评估失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "评估成功", result, nil)
}

// GetProducts 贷款产品匹配接口
// 路由: GET /uniapp/risk/riskcontroller/getProducts
func (rc *RiskController) GetProducts(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := risk.GetLoanProductsSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 从数据库获取 idcard
	// 1. 参数转换
	customerID, err := risk.GetCustomerIDFromData(validationResult.Data)
	if err != nil {
		results.Failed(c, "参数转换失败", err.Error())
		return
	}
	customerInfo, err := risk.NewRiskService(ctx).GetCustomerInfo(ctx, int64(customerID))
	if err != nil {
		results.Failed(c, "获取客户信息失败", err.Error())
		return
	}

	noCardResult := &risk.LoanProductsResponse{
		OverallCreditLimit: 50000,
	}
	// 验证客户信息完整性
	if customerInfo.Name == "" || customerInfo.IDCard == "" || customerInfo.Mobile == "" {
		results.Success(c, "获取成功", noCardResult, nil)
		return
	}

	result, err := risk.NewRiskService(ctx).GetLoanProducts(ctx, int64(customerID))
	if err != nil {
		results.Failed(c, "获取产品列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取成功", result, nil)
}
