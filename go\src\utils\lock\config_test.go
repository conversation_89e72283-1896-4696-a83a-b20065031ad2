package lock

import (
	"testing"
	"time"
)

// TestLockConfigurationLoading 测试锁配置加载
func TestLockConfigurationLoading(t *testing.T) {
	// 测试默认工厂配置
	factory := NewLockFactory()

	// 验证默认配置
	if factory.GetCurrentLockType() != LockTypeMemory {
		t.<PERSON><PERSON><PERSON>("默认配置应该是内存锁，实际: %s", factory.GetCurrentLockType())
	}

	// 测试配置更新
	config := &LockConfig{
		Type: LockTypeRedis,
		Redis: RedisConfig{
			DefaultExpiration: 60,
			RenewalInterval:   20,
			MaxRetryTimes:     5,
			RetryIntervalMs:   200,
			EnableWatchdog:    true,
		},
	}

	factory.UpdateConfig(config)

	// 验证配置是否生效
	if factory.IsUsingRedis() {
		if factory.GetCurrentLockType() != LockTypeRedis {
			t.Errorf("配置更新后应该使用Redis锁，实际: %s", factory.GetCurrentLockType())
		}
		t.Log("✅ Redis锁配置加载成功")
	} else {
		t.Log("⚠️  Redis不可用，使用内存锁降级")
	}
}

// TestLockConfigurationValues 测试锁配置值
func TestLockConfigurationValues(t *testing.T) {
	// 创建自定义配置
	config := &LockConfig{
		Type: LockTypeRedis,
		Redis: RedisConfig{
			DefaultExpiration: 45,
			RenewalInterval:   15,
			MaxRetryTimes:     3,
			RetryIntervalMs:   150,
			EnableWatchdog:    true,
		},
	}

	factory := NewLockFactory()
	factory.UpdateConfig(config)

	// 测试锁创建
	lock := factory.GetLock("test:config:values", 30*time.Second)
	if lock == nil {
		t.Fatal("应该能创建锁")
	}

	// 测试锁功能
	lock.Lock()
	defer lock.Unlock()

	// 验证锁状态
	if !lock.IsActuallyLocked() {
		t.Error("锁应该处于锁定状态")
	}

	status := lock.GetLockStatus()
	t.Logf("锁状态: 类型=%s, 锁定=%v, 警告=%v",
		status.LockType, status.IsLocked, status.Warnings)
}

// TestLockConfigurationMemoryType 测试内存锁配置
func TestLockConfigurationMemoryType(t *testing.T) {
	config := &LockConfig{
		Type: LockTypeMemory,
		Redis: RedisConfig{
			DefaultExpiration: 60,
			RenewalInterval:   20,
			MaxRetryTimes:     3,
			RetryIntervalMs:   100,
			EnableWatchdog:    true,
		},
	}

	factory := NewLockFactory()
	factory.UpdateConfig(config)

	if factory.GetCurrentLockType() != LockTypeMemory {
		t.Errorf("配置为内存锁时应该使用内存锁，实际: %s", factory.GetCurrentLockType())
	}

	// 测试内存锁功能
	lock := factory.GetLock("test:config:memory", 30*time.Second)
	lock.Lock()
	defer lock.Unlock()

	status := lock.GetLockStatus()
	if status.LockType != LockTypeMemory {
		t.Errorf("应该使用内存锁，实际: %s", status.LockType)
	}

	if !status.IsLocked {
		t.Error("锁应该处于锁定状态")
	}

	t.Log("✅ 内存锁配置测试通过")
}

// TestLockConfigurationHealthStatus 测试锁配置健康状态
func TestLockConfigurationHealthStatus(t *testing.T) {
	factory := NewLockFactory()

	// 获取健康状态
	health := factory.GetHealthStatus()

	// 验证健康状态包含必要信息
	requiredFields := []string{
		"configured_type",
		"actual_type",
		"is_healthy",
	}

	for _, field := range requiredFields {
		if _, exists := health[field]; !exists {
			t.Errorf("健康状态应该包含字段: %s", field)
		}
	}

	// 验证健康状态的基本信息
	if health["is_healthy"] != true {
		t.Error("健康状态应该为true")
	}

	t.Logf("健康状态: %+v", health)
}

// TestLockConfigurationGlobalFunctions 测试全局函数配置
func TestLockConfigurationGlobalFunctions(t *testing.T) {
	// 测试全局函数是否使用配置
	lock := GetLock("test:config:global", 30*time.Second)
	lock.Lock()
	defer lock.Unlock()

	status := lock.GetLockStatus()

	// 验证锁类型符合配置
	expectedType := GetDefaultFactory().GetCurrentLockType()
	if status.LockType != expectedType {
		t.Errorf("全局函数应该使用配置的锁类型 %s，实际: %s",
			expectedType, status.LockType)
	}

	t.Logf("✅ 全局函数配置测试通过: 类型=%s", status.LockType)
}
