<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import {
  IconRefresh,
  IconSearch,
  IconUserAdd,
} from '@arco-design/web-vue/es/icon';
import dayjs from 'dayjs';
import { getExpenseDetails } from '@/api/incomingOutgoings';
import { getChannelList } from '@/api/channel';
const queryFormRef = ref();
const queryForm = reactive({
  date_start: '',
  date_end: '',
});
const dateRange = ref([]);
const handleDateChange = (value: any) => {
  if (value && value.length > 0) {
    queryForm.date_start = value[0];
    queryForm.date_end = value[1];
  } else {
    queryForm.date_start = '';
    queryForm.date_end = '';
  }
};

const statistics = ref({});
const loading = ref(false);
const fetchData = async () => {
  loading.value = true;
  getExpenseDetails({
    ...queryForm,
    page: pagination.current,
    page_size: pagination.pageSize,
  }).then(res => {
    statistics.value = res.statistics;
    dataSource.value = res.list.data;
    pagination.total = res.list.total;
  }).finally(() => {
    loading.value = false;
  });
};

const dictChannels = ref();
onMounted(() => {
  getChannelList({ page: 1, pageSize: 1000 }).then(res => {
    dictChannels.value = res.data;
  })
  fetchData();
});

const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    render: ({ rowIndex }: any) => {
      return rowIndex + 1;
    }
  },
  {
    title: '订单编号',
    dataIndex: 'order_no',
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 120,
  },
  {
    title: '姓名',
    dataIndex: 'user_name',
    width: 150,
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
    width: 150,
  },
  {
    title: '支出账户',
    dataIndex: 'bank_card_no',
  },
  {
    title: '支付方式',
    dataIndex: 'payment_method',
  },
  {
    title: '时间',
    dataIndex: 'completed_at',
  }
];
const dataSource = ref([{}]);
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};
const handleReset = async () => {
  queryFormRef.value.resetFields();
  pagination.current = 1;
  queryForm.date_start = '';
  queryForm.date_end = '';
  dateRange.value = [];
  fetchData();
};
</script>

<template>
  <div class="container">
    <!-- 支出明细 -->
    <a-card title="支出明细" :bordered="false">
      <div class="stat-grid">
        <a-row :gutter="[24, 12]">
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">支出总额</div>
              <div class="stat-number">￥{{ statistics.total_expense || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">新用户支出总额</div>
              <div class="stat-number">￥{{ statistics.new_user_expense || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">老用户支出总额</div>
              <div class="stat-number">￥{{ statistics.old_user_expense || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">今日支出</div>
              <div class="stat-number">￥{{ statistics.today_expense || 0 }}</div>
            </div>
          </a-col>

          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">支出笔数</div>
              <div class="stat-number">{{ statistics.expense_count || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">支出订单数</div>
              <div class="stat-number">{{ statistics.expense_orders || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">支出人数</div>
              <div class="stat-number">{{ statistics.expense_users || 0 }}</div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 支出明细表单 -->
      <div class="due-form">
        <a-form
          ref="queryFormRef"
          :model="queryForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
          auto-label-width
          @submit="handleSearch"
        >
          <a-row :gutter="[24, 0]">
            <a-col :md="6" :sm="12">
              <a-form-item label="时间筛选">
                <a-range-picker
                  allow-clear
                  v-model="dateRange"
                  @change="handleDateChange"
                  :shortcuts="[
                    {
                      label: '近一周',
                      value: () => [dayjs().subtract(1, 'week'), dayjs()],
                    },
                    {
                      label: '近一月',
                      value: () => [dayjs().subtract(1, 'month'), dayjs()],
                    }
                  ]"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="channel_id" label="渠道来源">
                <a-select allow-clear v-model="queryForm.channel_id" placeholder="渠道来源">
                  <a-option v-for="item in dictChannels" :key="item.id" :value="item.id">{{ item.channel_name }}</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="user_name" label="用户姓名">
                <a-input allow-clear v-model="queryForm.user_name" placeholder="用户姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="mobile" label="手机号">
                <a-input allow-clear v-model="queryForm.mobile" placeholder="用户手机号" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  <template #icon>
                    <icon-search />
                  </template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <!-- 支出明细列表 -->
    <a-card class="table-card" title="支出明细列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="columns"
        :data="dataSource"
        :pagination="pagination"
        :bordered="{headerCell:true}"
        size="medium"
        :scroll="{ y: 500 }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :loading="loading"
      >
      </a-table>
    </a-card>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}

.stat-grid {
  .arco-col {
    height: 80px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background-color: var(--color-bg-1);
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .stat-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-2);
    }

    .stat-number {
      font-size: 16px;
      color: rgb(var(--primary-6));
      margin-top: 10px;
      font-weight: bold;
    }
  }
}

.due-form {
  margin-top: 20px;
}

.table-card {
  margin-top: 10px;
}
</style>