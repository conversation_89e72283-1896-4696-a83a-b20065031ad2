import { defHttp } from '@/utils/http';

//类型
export interface LoginData {
  username: string;
  password: string;
}
enum Api {
  createRule = '/productrules/list/createRule',
  updateRule = '/productrules/list/updateRule',
  delRule = '/productrules/list/delRule',
  getRule = '/productrules/list/getRules',
  CalculateRepaymentSchedule = '/payment/manager/calculateRepaymentSchedule',
  detailRule = '/productrules/list/getRuleDetail',
}

//提交菜单
export function createRule(params: any) {
  return defHttp.post({ url: Api.createRule, params:params}, { errorMessageMode: 'message' });
}

//更新状态
export function updateRule(params: object) {
    return defHttp.post({ url: Api.updateRule, params:params}, { errorMessageMode: 'message' });
}

//数据列表
export function getRule(params: object) {
  return defHttp.get({ url: Api.getRule, params:params }, { errorMessageMode: 'none' });
}

//删除规则
// export function delRule(id: number) {
//     return defHttp.delete({ url: `${Api.delRule}?id=${id}`, params:{id}}, { errorMessageMode: 'message' });
// }
export function delRule(params: object) {
    return defHttp.delete({ url: Api.delRule, params:params}, { errorMessageMode: 'message' });
}
// 计算还款计划 /business/payment/manager/calculateRepaymentSchedule
export function calculateRepaymentSchedule(params: object) {
  return defHttp.post({ url: Api.CalculateRepaymentSchedule, params:params}, { errorMessageMode: 'message' });
}

// 获取还款计划详情
export function detailRule(params: object) {
  return defHttp.get({ url: Api.detailRule, params:params}, { errorMessageMode: 'message' });
}
