<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import {
  IconRefresh,
  IconSearch,
  IconUserAdd,
} from '@arco-design/web-vue/es/icon';
import dayjs from 'dayjs';
import { getIncomeDetails } from '@/api/incomingOutgoings';
const queryFormRef = ref();
const queryForm = reactive({
  order_no: '',
  user_name: '',
  mobile: '',
  fund_type: '',
  payment_method: '',
  payment_status: '',
  payment_time_start: '',
  payment_time_end: '',
  bill_time_start: '',
  bill_time_end: '',
});
const payment_time = ref([])
const bill_time = ref([])
const statistics = ref({})
const handlePaymentTimeChange = (val: any) => {
  if (val && val.length) {
    queryForm.payment_time_start = val[0]
    queryForm.payment_time_end = val[1]
  }else{
    queryForm.payment_time_start = ''
    queryForm.payment_time_end = ''
  }
}
const handleBillTimeChange = (val: any) => {
  if (val && val.length) {
    queryForm.bill_time_start = val[0]
    queryForm.bill_time_end = val[1]
  }else{
    queryForm.bill_time_start = ''
    queryForm.bill_time_end = ''
  }
}
const loading = ref(false);
const fetchData = async () => {
  loading.value = true;
  getIncomeDetails({
    ...queryForm,
    page: pagination.current,
    page_size: pagination.pageSize,
  }).then((res) => {
    statistics.value = res.statistics;
    dataSource.value = res.list.data;
    pagination.total = res.list.total;
  }).finally(() => {
    loading.value = false;
  })
};
onMounted(() => {
  fetchData();
})
const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    width: 50,
    render: ({ rowIndex }: any) => {
      return rowIndex + 1
    }
  },
  {
    title: '订单编号',
    dataIndex: 'order_no',
  },
  {
    title: '收款金额',
    dataIndex: 'amount',
    width: 120,
  },
  {
    title: '姓名',
    dataIndex: 'user_name',
    width: 150,
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
    width: 150,
  },
  {
    title: '款项类型',
    dataIndex: 'fund_type_text',
    width: 120,
  },
  {
    title: '收款方式',
    dataIndex: 'payment_method_text',
  },
  {
    title: '期数',
    dataIndex: 'period_number',
    width: 150,
  },
  {
    title: '收款时间',
    dataIndex: 'completed_at',
  },
  {
    title: '账单时间',
    dataIndex: 'due_date',
    width: 150,
  }
];
const dataSource = ref([{}]);
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};
const handleReset = async () => {
  queryFormRef.value.resetFields();
  pagination.current = 1;
  queryForm.payment_time_start = '';
  queryForm.payment_time_end = '';
  queryForm.bill_time_start = '';
  queryForm.bill_time_end = '';
  bill_time.value = []
  payment_time.value = []
  fetchData();
};
</script>

<template>
  <div class="container">
    <!-- 收入明细 -->
    <a-card title="收入明细" :bordered="false">
      <div class="stat-grid">
        <a-row :gutter="[24, 12]">
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">收款合计</div>
              <div class="stat-number">{{ statistics.total_income || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">退款合计</div>
              <div class="stat-number">{{ statistics.total_refund || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">到期收款</div>
              <div class="stat-number">{{ statistics.due_income || 0 }}</div>
            </div>
          </a-col>

          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">逾期收款</div>
              <div class="stat-number">{{ statistics.overdue_income || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">提前处理收款</div>
              <div class="stat-number">{{ statistics.early_income || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">收款人数</div>
              <div class="stat-number">{{ statistics.total_customers || 0 }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">收款订单数</div>
              <div class="stat-number">{{ statistics.total_orders || 0 }}</div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 收入明细表单 -->
      <div class="due-form">
        <a-form
          ref="queryFormRef"
          :model="queryForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
          auto-label-width
          @submit="handleSearch"
        >
          <a-row :gutter="[24, 0]">
            <a-col :md="6" :sm="12">
              <a-form-item field="order_no" label="订单编号">
                <a-input v-model="queryForm.order_no" placeholder="订单编号" allow-clear/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="user_name" label="用户姓名">
                <a-input v-model="queryForm.user_name" placeholder="用户姓名" allow-clear/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="mobile" label="手机号">
                <a-input v-model="queryForm.mobile" placeholder="用户手机号" allow-clear/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="fund_type" label="款项类型">
                <a-select v-model="queryForm.fund_type" placeholder="款项类型" allow-clear>
                  <a-option :value="0">收款</a-option>
                  <a-option :value="1">退款</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="payment_method" label="收款方式">
                <a-select v-model="queryForm.payment_method" placeholder="收款方式" allow-clear>
                  <a-option :value="0">资管支付</a-option>
                  <a-option :value="1">资管代扣</a-option>
                  <a-option :value="2">担保支付</a-option>
                  <a-option :value="3">担保代扣</a-option>
                  <a-option :value="4">支付宝支付（线下）</a-option>
                  <a-option :value="5">微信支付（线下）</a-option>
                  <a-option :value="6">银行卡支付（线下）</a-option>
                  <a-option :value="7">信用卡支付（线下）</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="payment_status" label="收款状态">
                <a-select v-model="queryForm.payment_status" placeholder="收款状态" allow-clear>
                  <a-option :value="0">提前收款</a-option>
                  <a-option :value="1">到期收款</a-option>
                  <a-option :value="2">逾期收款</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item label="收款时间">
                <a-range-picker
                  :shortcuts="[
                    {
                      label: '近一周',
                      value: () => [dayjs().subtract(1, 'week'), dayjs()],
                    },
                    {
                      label: '近一月',
                      value: () => [dayjs().subtract(1, 'month'), dayjs()],
                    }
                  ]"
                  v-model="payment_time"
                  @change="handlePaymentTimeChange"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item label="账单时间">
                <a-range-picker
                  :shortcuts="[
                    {
                      label: '近一周',
                      value: () => [dayjs().subtract(1, 'week'), dayjs()],
                    },
                    {
                      label: '近一月',
                      value: () => [dayjs().subtract(1, 'month'), dayjs()],
                    }
                  ]"
                  v-model="bill_time"
                  @change="handleBillTimeChange"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  <template #icon>
                    <icon-search />
                  </template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
<!--                <a-button>
                  <template #icon>
                    <icon-export />
                  </template>
                  导出
                </a-button>-->
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <!-- 收入明细列表 -->
    <a-card class="table-card" title="收入明细列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="columns"
        :data="dataSource"
        :pagination="pagination"
        :bordered="{headerCell:true}"
        size="medium"
        :scroll="{ y: '500px' }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :loading="loading"
      >
      </a-table>
    </a-card>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}

.stat-grid {
  .arco-col {
    height: 80px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background-color: var(--color-bg-1);
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .stat-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-2);
    }

    .stat-number {
      font-size: 16px;
      color: rgb(var(--primary-6));
      margin-top: 10px;
      font-weight: bold;
    }
  }
}

.due-form {
  margin-top: 20px;
}

.table-card {
  margin-top: 10px;
}
</style>