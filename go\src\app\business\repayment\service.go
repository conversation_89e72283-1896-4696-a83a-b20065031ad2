package repayment

import (
	"context"
	"encoding/json"
	"errors"
	"fincore/global"
	"fincore/model"
	"fincore/route/middleware"
	"fincore/thirdparty/payment/sumpay"
	"fincore/utils/lock"
	"fincore/utils/log"
	"fincore/utils/shopspringutils"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

// 支付相关常量
const (
	PaymentCurrency  = "CNY"   // 币种
	PaymentGoodsName = "互金产品"  // 商品名称
	PaymentTradeCode = "T0002" // 交易码
	PaymentGoodsNum  = 1       // 商品数量
	PaymentGoodsType = 1       // 商品类型：虚拟物品
)

// PaymentService 支付服务
type PaymentService struct {
	billModel        *model.BusinessRepaymentBillsService
	transactionModel *model.BusinessPaymentTransactionsService
	bankCardModel    *model.BusinessBankCardsService
	orderModel       *model.BusinessLoanOrdersService
	operationModel   *model.BusinessOrderOperationLogsService
	ctx              context.Context
	logger           *log.Logger
}

func WithLogger(logger *log.Logger) func(*PaymentService) {
	return func(service *PaymentService) {
		service.logger = logger
	}
}

// NewPaymentService 创建支付服务实例
func NewPaymentService(ctx context.Context, opts ...func(*PaymentService)) *PaymentService {
	service := &PaymentService{
		billModel:        model.NewBusinessRepaymentBillsService(ctx),
		transactionModel: model.NewBusinessPaymentTransactionsService(ctx),
		bankCardModel:    model.NewBusinessBankCardsService(ctx),
		orderModel:       model.NewBusinessLoanOrdersService(ctx),
		operationModel:   model.NewBusinessOrderOperationLogsService(),
		ctx:              ctx,
		logger:           log.Repayment().WithContext(ctx),
	}
	for _, opt := range opts {
		opt(service)
	}
	return service
}

// 客户主动还款状态
const (
	// 还款成功
	CustomerRepaymentStatusSuccess = iota + 1
	// 部分还款成功
	CustomerRepaymentStatusPartiallySuccess
	// 还款失败
	CustomerRepaymentStatusFailed
	// 还款申请已提交
	CustomerRepaymentStatusSubmitted
)

type PaymentStatus struct {
	Status    string `json:"status"`     // 还款状态
	StatusRaw int    `json:"status_raw"` // 还款状态流水原始值
}

func (s *PaymentService) QueryTransactionsStatus(transactionsNo []string) (paymentStatus PaymentStatus, err error) {

	paymentStatus = PaymentStatus{
		Status:    "未知状态",
		StatusRaw: 0,
	}

	if len(transactionsNo) == 0 {
		return paymentStatus, fmt.Errorf("交易流水号不能为空")
	}

	var transactionsStatus = make(map[string]int)

	for _, transactionNo := range transactionsNo {
		transaction, err := s.QueryPaymentStatus(transactionNo)
		if err != nil {
			return paymentStatus, fmt.Errorf("查询交易记录失败: %v", err)
		}
		transactionsStatus[transactionNo] = transaction.StatusRaw
	}

	if len(transactionsStatus) == 0 {
		return paymentStatus, fmt.Errorf("查询交易记录失败")
	}

	if len(transactionsStatus) == 1 {
		if transactionsStatus[transactionsNo[0]] == model.TransactionStatusSuccess {
			paymentStatus.Status = "还款成功"
			paymentStatus.StatusRaw = CustomerRepaymentStatusSuccess
		}

		if transactionsStatus[transactionsNo[0]] == model.TransactionStatusFailed {
			paymentStatus.Status = "还款失败"
			paymentStatus.StatusRaw = CustomerRepaymentStatusFailed
		}

		if transactionsStatus[transactionsNo[0]] == model.TransactionStatusSubmitted {
			paymentStatus.Status = "还款申请已提交"
			paymentStatus.StatusRaw = CustomerRepaymentStatusSubmitted
		}

	} else {
		var statusOne, statusTwo int
		statusOne = transactionsStatus[transactionsNo[0]]
		statusTwo = transactionsStatus[transactionsNo[1]]

		// 1. 两个都成功为成功
		// 2. 两个失败为失败
		// 3. 一个成功一个失败为部分成功
		// 4. 只要有一个为已提交则为已提交状态

		// 1. 两个都成功为成功
		if statusOne == model.TransactionStatusSuccess && statusTwo == model.TransactionStatusSuccess {
			paymentStatus.Status = "还款成功"
			paymentStatus.StatusRaw = CustomerRepaymentStatusSuccess
		}
		// 2. 两个失败为失败
		if statusOne == model.TransactionStatusFailed && statusTwo == model.TransactionStatusFailed {
			paymentStatus.Status = "还款失败"
			paymentStatus.StatusRaw = CustomerRepaymentStatusFailed
		}
		// 3. 一个成功一个失败为部分成功
		if (statusOne == model.TransactionStatusSuccess && statusTwo == model.TransactionStatusFailed) ||
			(statusOne == model.TransactionStatusFailed && statusTwo == model.TransactionStatusSuccess) {
			paymentStatus.Status = "部分还款成功"
			paymentStatus.StatusRaw = CustomerRepaymentStatusPartiallySuccess
		}
		// 4. 只要有一个为已提交则为已提交状态
		if statusOne == model.TransactionStatusSubmitted || statusTwo == model.TransactionStatusSubmitted {
			paymentStatus.Status = "还款申请已提交"
			paymentStatus.StatusRaw = CustomerRepaymentStatusSubmitted
		}

	}

	return

}

// QueryPaymentStatus 查询第三方支付状态并更新本地流水
func (s *PaymentService) QueryPaymentStatus(transactionNo string) (*QueryPaymentStatusResponse, error) {
	// 加锁
	queryLock := lock.GetPaymentRefreshLock(transactionNo, s.logger).Lock()
	defer queryLock.Unlock()

	s.logger.WithFields(
		log.String("transaction_no", transactionNo),
		log.String("action", "query_payment_status_start"),
	).Info("开始查询支付状态")

	// 1. 查询本地交易记录
	transaction, err := s.transactionModel.GetTransactionByNo(transactionNo)
	if err != nil {
		s.logger.WithFields(
			log.String("transaction_no", transactionNo),
			log.String("action", "query_local_transaction_failed"),
			log.ErrorField(err),
		).Error("查询本地交易记录失败")
		return nil, fmt.Errorf("查询交易记录失败: %v", err)
	}

	// 2. 如果不是已提交状态的交易 还是返回流水的状态
	if transaction.Status != model.TransactionStatusSubmitted {
		s.logger.WithFields(
			log.String("transaction_no", transactionNo),
			log.Int("status", transaction.Status),
			log.String("action", "return_local_status"),
		).Info("交易非已提交状态，返回本地状态")
		return &QueryPaymentStatusResponse{
			Status:    getStatusString(transaction.Status),
			StatusRaw: transaction.Status,
		}, nil
	}

	// 3. 检查第三方订单号
	if transaction.ThirdPartyOrderNo == "" {
		s.logger.WithFields(
			log.String("transaction_no", transactionNo),
			log.String("action", "third_party_order_no_empty"),
		).Error("第三方订单号为空")
		return nil, fmt.Errorf("第三方订单号为空，无法查询")
	}

	// 4. 调用第三方支付查询接口
	paymentService, err := sumpay.NewSumpayService()
	if err != nil {
		s.logger.WithFields(
			log.String("transaction_no", transactionNo),
			log.String("action", "get_payment_service_failed"),
			log.ErrorField(err),
		).Error("获取支付服务失败")
		return nil, fmt.Errorf("获取支付服务失败: %v", err)
	}

	// 5. 使用QueryPayOrder接口查询订单状态

	// 根据交易类型确定商户类型
	merchant, err := GetMerNoByWithholdType(*transaction.WithholdType)
	if err != nil {
		s.logger.WithFields(
			log.String("transaction_no", transactionNo),
			log.String("action", "get_merchant_config_failed"),
			log.ErrorField(err),
		).Error("获取商户配置失败")
		return nil, fmt.Errorf("获取商户配置失败: %v", err)
	}
	queryRequest := &sumpay.QueryPayOrderRequest{
		BaseRequest: sumpay.NewBaseRequest("fosun.sumpay.api.trade.order.search.merchant",
			merchant.AppId, merchant.MerNo),
		OrderNo:      transaction.ThirdPartyOrderNo,
		MerchantConf: merchant,
	}

	queryResponse, err := paymentService.QueryPayOrder(queryRequest)
	if err != nil {
		return nil, fmt.Errorf("查询第三方支付状态失败: %v", err)
	}
	// 6. 检查查询是否成功
	if !queryResponse.IsSuccess() {
		s.logger.WithFields(
			log.String("transaction_no", transactionNo),
			log.String("third_party_order_no", transaction.ThirdPartyOrderNo),
			log.String("resp_code", queryResponse.RespCode),
			log.String("resp_msg", queryResponse.RespMsg),
			log.String("action", "third_party_query_failed"),
		).Error("第三方查询失败")
		// 查询失败，记录错误但不更新交易状态
		return nil, fmt.Errorf("第三方查询失败: %s - %s", queryResponse.RespCode, queryResponse.RespMsg)
	}

	// 7. 解析查询响应数据
	businessResp, err := queryResponse.GetQueryPayOrderResponseData()
	if err != nil {
		return nil, fmt.Errorf("解析查询响应数据失败: %v", err)
	}

	// 8. 根据查询结果更新本地状态
	s.logger.WithFields(
		log.String("transaction_no", transactionNo),
		log.String("third_party_status", businessResp.Status),
		log.String("action", "process_third_party_status"),
	).Info("处理第三方支付状态")

	var updateStatus int
	var errorCode, errorMessage string

	switch businessResp.Status {
	case "1": // 成功
		updateStatus = model.TransactionStatusSuccess
		s.logger.WithFields(
			log.String("transaction_no", transactionNo),
			log.String("action", "payment_success"),
		).Info("支付成功")
		if transaction.BillID != nil {
			err = s.updateBillAndOrderPaidAmount(transaction.OrderID, *transaction.BillID, float64(transaction.Amount))
			if err != nil {
				s.logger.WithFields(
					log.String("transaction_no", transactionNo),
					log.Int("bill_id", *transaction.BillID),
					log.String("action", "update_bill_amount_failed"),
					log.ErrorField(err),
				).Error("更新账单和订单已还金额失败")
				return nil, fmt.Errorf("更新账单和订单已还金额失败: %v", err)
			}
		}

	case "0": // 失败
		updateStatus = model.TransactionStatusFailed
		// 使用第三方返回的错误信息，如果没有则使用默认值
		if businessResp.ErrorCode != "" {
			errorCode = businessResp.ErrorCode
		} else {
			errorCode = "PAYMENT_FAILED"
		}
		if businessResp.ErrorMsg != "" {
			errorMessage = businessResp.ErrorMsg
		} else {
			errorMessage = "支付失败"
		}

	case "2": // 处理中
		updateStatus = model.TransactionStatusSubmitted // 保持已提交状态

	default:
		updateStatus = model.TransactionStatusFailed
		errorCode = "UNKNOWN_STATUS"
		errorMessage = fmt.Sprintf("未知的支付状态: %s", businessResp.Status)
	}

	// 9. 更新本地交易状态
	updateMap := map[string]interface{}{
		"status": updateStatus,
	}
	if errorCode != "" {
		updateMap["error_code"] = errorCode
	}
	if errorMessage != "" {
		updateMap["error_message"] = errorMessage
	}

	// 更新流水记录需要加锁，避免其他地方查询数据出现一致性问题
	transactionLock := lock.GetOrderTransactionLock(transaction.OrderID, nil).Lock()
	err = s.transactionModel.UpdateTransactionStatus(
		nil,
		model.UpdateTransactionStatusResultWhere{
			TransactionNo: transactionNo,
		},
		updateMap,
	)
	transactionLock.Unlock()
	if err != nil {
		return nil, fmt.Errorf("更新交易状态失败: %v", err)
	}

	// 10. 重新查询更新后的交易记录
	updatedTransaction, err := s.transactionModel.GetTransactionByNo(transactionNo)
	if err != nil {
		return nil, fmt.Errorf("查询更新后的交易记录失败: %v", err)
	}

	// 11. 构造响应
	s.logger.WithFields(
		log.String("transaction_no", transactionNo),
		log.String("final_status", getStatusString(updatedTransaction.Status)),
		log.String("action", "query_payment_status_complete"),
	).Info("查询支付状态完成")

	response := &QueryPaymentStatusResponse{
		TransactionNo:        updatedTransaction.TransactionNo,
		Status:               getStatusString(updatedTransaction.Status),
		StatusRaw:            updatedTransaction.Status,
		Amount:               float64(updatedTransaction.Amount),
		ChannelTransactionNo: updatedTransaction.ChannelTransactionNo,
		UpdatedAt:            updatedTransaction.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	if updatedTransaction.ErrorCode != "" {
		response.ErrorCode = updatedTransaction.ErrorCode
	}
	if updatedTransaction.ErrorMessage != "" {
		response.ErrorMessage = updatedTransaction.ErrorMessage
	}

	return response, nil
}

// CreateRepayment 创建还款支付（用户主动还款）
func (s *PaymentService) CreateRepayment(ctx *gin.Context, billID int, bankCardID int) (*CreateRepaymentResponse, error) {
	s.logger.WithFields(
		log.Int("bill_id", billID),
		log.Int("bank_card_id", bankCardID),
		log.String("action", "create_repayment_start"),
	).Info("开始处理还款支付")

	// 1. 获取当前登录用户ID
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	if claim == nil {
		return nil, errors.New("用户未登录")
	}
	currentUserID := claim.ID

	// 2. 调用内部还款方法
	return s.createRepaymentInternal(int(currentUserID), billID, bankCardID, false)
}

// createRepaymentInternal 内部还款方法（兼容用户主动还款和系统自动代扣）
func (s *PaymentService) createRepaymentInternal(userID int, billID int, bankCardID int, isSystemWithhold bool) (*CreateRepaymentResponse, error) {
	// 🔒 添加还款维度锁，防止并发操作
	lockKey := fmt.Sprintf("repayment:bill:%d", billID)
	repaymentLock := lock.GetLock(lockKey, 30*time.Second)
	log.WithFields(
		log.String("lock_key", lockKey),
		log.Int("bill_id", billID),
		log.String("action", "repayment_lock_acquired"),
	).Info("还款加锁成功")

	repaymentLock.Lock()
	defer repaymentLock.Unlock()
	// 1. 查询还款账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return nil, fmt.Errorf("查询还款账单失败: %v", err)
	}

	// 2. 验证账单所属用户
	if bill.UserID != userID {
		return nil, errors.New("无权限操作此账单")
	}

	// 3. 验证银行卡属于当前用户
	bankCardService := model.NewBusinessBankCardsService(s.ctx)
	bankCard, err := bankCardService.GetBankCardsByCondition(model.QueryBankCardsParams{
		CardID: bankCardID,
	})
	if err != nil {
		return nil, fmt.Errorf("查询银行卡信息失败: %v", err)
	}
	// 检查是否找到记录
	if bankCard == nil {
		return nil, errors.New("银行卡不存在")
	}
	if bankCard.UserID != userID {
		return nil, errors.New("银行卡不属于当前用户")
	}

	// 4. 处理还款逻辑
	response := &CreateRepaymentResponse{}

	// 5. 确定交易类型
	transactionType := model.TransactionTypeRepayment
	if isSystemWithhold {
		transactionType = model.TransactionTypeWithhold
	}
	// 计算已支付的金额
	guaranteePaidAmount, assetPaidAmount, err := shopspringutils.CalculateSubmittedAmounts(billID)
	if err != nil {
		return nil, err
	}
	// 计算减免后的实际应还金额
	remainingGuaranteeAmount, remainingAssetAmount := shopspringutils.CalculateRemainingAmounts(float64(bill.TotalDueAmount),
		float64(bill.TotalWaiveAmount), float64(bill.DueGuaranteeFee), float64(bill.AssetManagementEntry))
	// 计算实际需要支付的资管费金额（减免后应还金额 - 已支付金额）
	needPaidAssetAmount := shopspringutils.SubtractAmountsWithDecimal(remainingAssetAmount, assetPaidAmount)
	needPaidGuaranteeAmount := shopspringutils.SubtractAmountsWithDecimal(remainingGuaranteeAmount, guaranteePaidAmount)

	s.logger.WithFields(
		log.Int("bill_id", billID),
		log.Float64("needPaidAssetAmount", needPaidAssetAmount),
		log.Float64("needPaidGuaranteeAmount", needPaidGuaranteeAmount),
		log.String("action", "calculate_need_paid_amounts"),
	).Info("应还担保和资管费金额")

	// 6.1 发起资管费支付
	if needPaidAssetAmount > 0 {
		assetResult, err := s.processRepayment(
			userID,
			billID,
			bankCardID,
			needPaidAssetAmount,
			WithholdTypeAsset,
			transactionType,
			"",
			0,
		)
		if err != nil {
			s.logger.WithError(err).WithFields(
				log.Int("bill_id", billID),
				log.Float64("amount", needPaidAssetAmount),
				log.String("payment_type", "asset_management"),
			).Error("创建资管费支付失败")
		}
		response.AssetPayment = assetResult
	}

	// 6.2 发起担保费支付
	if needPaidGuaranteeAmount > 0 {
		guaranteeResult, err := s.processRepayment(
			userID,
			billID,
			bankCardID,
			needPaidGuaranteeAmount,
			WithholdTypeGuarantee,
			transactionType,
			"",
			0,
		)
		if err != nil {
			s.logger.WithError(err).WithFields(
				log.Int("bill_id", billID),
				log.Float64("amount", needPaidGuaranteeAmount),
				log.String("payment_type", "guarantee_fee"),
			).Error("创建担保费支付失败")
		}
		response.GuaranteePayment = guaranteeResult
	}
	if response.GuaranteePayment == nil && response.AssetPayment == nil {
		return nil, errors.New("创建还款支付失败")
	}

	s.logger.WithFields(
		log.Int("bill_id", billID),
		log.Bool("is_system_withhold", isSystemWithhold),
		log.String("action", "repayment_created"),
	).Info("还款支付创建完成")
	return response, nil
}

// SystemAutoWithholdBatch 系统自动批量代扣（内部方法，不对外暴露）
func (s *PaymentService) SystemAutoWithholdBatch() (*SystemAutoWithholdBatchResponse, error) {
	s.logger.WithField("action", "system_auto_withhold_batch_start").Info("开始执行系统自动批量代扣")

	// 1. 查询需要代扣的账单
	bills, err := s.getPendingWithholdBills()
	if err != nil {
		return nil, fmt.Errorf("查询待代扣账单失败: %v", err)
	}
	if len(bills) == 0 {
		s.logger.WithField("action", "no_bills_found").Info("未找到需要代扣的账单")
		return &SystemAutoWithholdBatchResponse{
			TotalCount:   0,
			SuccessCount: 0,
			FailureCount: 0,
			Results:      []SystemAutoWithholdResult{},
		}, nil
	}

	s.logger.WithFields(
		log.Int("count", len(bills)),
		log.String("action", "bills_found"),
	).Info("找到待代扣账单")

	// 2. 批量处理代扣
	var results []SystemAutoWithholdResult
	var successCount, failureCount int

	for _, bill := range bills {
		result := s.processSystemAutoWithhold(bill)
		results = append(results, result)

		if result.Success {
			successCount++
		} else {
			failureCount++
		}
	}

	response := &SystemAutoWithholdBatchResponse{
		TotalCount:   len(bills),
		SuccessCount: successCount,
		FailureCount: failureCount,
		Results:      results,
	}

	s.logger.WithFields(
		log.Int("total", len(bills)),
		log.Int("success", successCount),
		log.Int("failure", failureCount),
		log.String("action", "system_auto_withhold_batch_complete"),
	).Info("系统自动批量代扣完成")
	return response, nil
}

// processSystemAutoWithhold 处理单个账单的系统自动代扣
func (s *PaymentService) processSystemAutoWithhold(bill model.BusinessRepaymentBills) SystemAutoWithholdResult {
	result := SystemAutoWithholdResult{
		BillID:  bill.ID,
		UserID:  bill.UserID,
		OrderID: bill.OrderID,
		Success: false,
	}

	// 1. 获取系统代扣配置
	maxFailCount := global.App.Config.SystemDeduct.MaxFailCount
	if maxFailCount <= 0 {
		maxFailCount = 15 // 默认值
	}

	// 2. 查询失败次数未超过限制的银行卡
	bankCardService := model.NewBusinessBankCardsService(context.Background())
	bankCards, err := bankCardService.GetBankCardsWithFailCountLimit(bill.UserID, maxFailCount)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("查询用户银行卡失败: %v", err)
		s.logger.WithError(err).WithFields(
			log.Int("user_id", bill.UserID),
			log.String("action", "query_bank_cards_failed"),
		).Error("查询用户银行卡失败")
		return result
	}

	if len(bankCards) == 0 {
		result.ErrorMessage = "用户未绑定银行卡或所有银行卡失败次数已达上限"
		s.logger.WithFields(
			log.Int("user_id", bill.UserID),
			log.Int("max_fail_count", maxFailCount),
			log.String("action", "no_available_bank_cards"),
		).Warn("用户未绑定银行卡或所有银行卡失败次数已达上限")
		return result
	}

	// 2. 逐一尝试用户的每张银行卡进行代扣
	var lastErr error
	for i, bankCard := range bankCards {
		// 只尝试已绑定状态的银行卡
		if bankCard.CardStatus != model.CardStatusBound {
			s.logger.WithFields(
				log.Int("bank_card_id", bankCard.ID),
				log.Int("status", bankCard.CardStatus),
				log.String("action", "skip_unbound_card"),
			).Info("跳过未绑定银行卡")
			continue
		}

		s.logger.WithFields(
			log.Int("bill_id", bill.ID),
			log.Int("user_id", bill.UserID),
			log.Int("bank_card_id", bankCard.ID),
			log.Int("card_index", i+1),
			log.String("action", "try_system_withhold"),
		).Info("尝试使用银行卡进行系统自动代扣")

		// 3. 调用内部还款方法进行代扣
		repaymentResponse, err := s.createRepaymentInternal(bill.UserID, bill.ID, bankCard.ID, true)
		if err != nil {
			lastErr = err
			s.logger.WithError(err).WithFields(
				log.Int("bill_id", bill.ID),
				log.Int("user_id", bill.UserID),
				log.Int("bank_card_id", bankCard.ID),
				log.String("action", "bank_card_withhold_failed"),
			).Error("银行卡代扣失败")
			// 增加系统代扣失败次数
			if incrementErr := bankCardService.IncrementSystemDeductFailCount(bankCard.BankCardNo, bill.UserID); incrementErr != nil {
				s.logger.WithError(incrementErr).WithFields(
					log.String("bank_card_no", bankCard.BankCardNo),
					log.Int("user_id", bill.UserID),
					log.String("action", "increment_fail_count_failed"),
				).Error("增加系统代扣失败次数失败")
			}
			continue
		}

		// 4. 检查代扣结果状态
		isSuccess := s.checkRepaymentSuccess(repaymentResponse)
		if !isSuccess {
			lastErr = fmt.Errorf("代扣支付失败")
			s.logger.WithFields(
				log.Int("bill_id", bill.ID),
				log.Int("user_id", bill.UserID),
				log.Int("bank_card_id", bankCard.ID),
				log.String("action", "bank_card_payment_failed"),
			).Error("银行卡代扣支付失败")
			// 增加系统代扣失败次数
			if incrementErr := bankCardService.IncrementSystemDeductFailCount(bankCard.BankCardNo, bill.UserID); incrementErr != nil {
				s.logger.WithError(incrementErr).WithFields(
					log.String("bank_card_no", bankCard.BankCardNo),
					log.Int("user_id", bill.UserID),
					log.String("action", "increment_fail_count_failed"),
				).Error("增加系统代扣失败次数失败")
			}
			continue
		}

		// 5. 代扣成功，记录结果并返回
		result.Success = true
		result.BankCardID = bankCard.ID
		result.RepaymentResponse = repaymentResponse

		s.logger.WithFields(
			log.Int("bill_id", bill.ID),
			log.Int("user_id", bill.UserID),
			log.Int("bank_card_id", bankCard.ID),
			log.String("action", "system_withhold_success"),
		).Info("系统自动代扣成功")
		return result
	}

	// 6. 所有银行卡都尝试失败
	if lastErr != nil {
		result.ErrorMessage = fmt.Sprintf("所有银行卡代扣均失败，最后错误: %v", lastErr)
	} else {
		result.ErrorMessage = "没有可用的银行卡进行代扣"
	}
	s.logger.WithFields(
		log.Int("bill_id", bill.ID),
		log.Int("user_id", bill.UserID),
		log.String("error_message", result.ErrorMessage),
		log.String("action", "system_withhold_all_failed"),
	).Error("系统自动代扣失败")
	return result
}

// updateTransactionStatus 更新交易状态
func (s *PaymentService) updateTransactionStatus(transactionNo string, status int, message string) error {
	updateMap := map[string]interface{}{
		"status":        status,
		"error_message": message,
	}
	where := model.UpdateTransactionStatusResultWhere{
		TransactionNo: transactionNo,
	}
	return s.transactionModel.UpdateTransactionStatus(nil, where, updateMap)
}

// updateTransactionStatusWithDetails 更新交易状态（包含详细信息）
func (s *PaymentService) updateTransactionStatusWithDetails(transactionNo string, status int, message string, channelTransactionNo string, thirdPartyOrderNo string) error {
	s.logger.WithFields(
		log.String("transaction_no", transactionNo),
		log.Int("status", status),
		log.String("channel_transaction_no", channelTransactionNo),
		log.String("third_party_order_no", thirdPartyOrderNo),
		log.String("action", "update_transaction_status_start"),
	).Info("开始更新交易状态")
	updateMap := map[string]interface{}{
		"status":                 status,
		"error_message":          message,
		"channel_transaction_no": channelTransactionNo,
		"third_party_order_no":   thirdPartyOrderNo,
	}
	where := model.UpdateTransactionStatusResultWhere{
		TransactionNo: transactionNo,
	}
	err := s.transactionModel.UpdateTransactionStatus(nil, where, updateMap)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("transaction_no", transactionNo),
			log.String("action", "update_transaction_status_failed"),
		).Error("更新交易状态失败")
		return err
	}
	s.logger.WithFields(
		log.String("transaction_no", transactionNo),
		log.Int("status", status),
		log.String("action", "update_transaction_status_success"),
	).Info("更新交易状态成功")
	return nil
}

// ManualWithhold 管理员手动代扣
func (s *PaymentService) ManualWithhold(ctx *gin.Context, billID int, bankCardID int, amount float64, remark string, withholdType string, adminID int) (*PaymentResult, error) {
	s.logger.WithFields(
		log.Int("bill_id", billID),
		log.Int("bank_card_id", bankCardID),
		log.Float64("amount", amount),
		log.Int("admin_id", adminID),
		log.String("withhold_type", withholdType),
		log.String("action", "manual_withhold_start"),
	).Info("开始处理手动代扣")
	// 🔒 添加还款维度锁，防止并发操作
	lockKey := fmt.Sprintf("repayment:bill:%d", billID)
	repaymentLock := lock.GetLock(lockKey, 30*time.Second)
	log.WithFields(
		log.String("lock_key", lockKey),
		log.Int("bill_id", billID),
		log.String("action", "repayment_lock_acquired"),
	).Info("还款加锁成功")

	repaymentLock.Lock()
	defer repaymentLock.Unlock()

	// 1. 通过bill_id查询账单信息获取user_id
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return nil, fmt.Errorf("查询账单信息失败: %v", err)
	}
	if bill == nil {
		return nil, fmt.Errorf("账单不存在")
	}

	// 2. 验证指定的银行卡是否存在且属于该用户
	bankCardService := model.NewBusinessBankCardsService(s.ctx)
	bankCard, err := bankCardService.GetBankCardsByCondition(model.QueryBankCardsParams{CardID: bankCardID})
	if err != nil {
		return nil, fmt.Errorf("查询银行卡信息失败: %v", err)
	}
	if bankCard == nil {
		return nil, fmt.Errorf("银行卡不存在")
	}
	if bankCard.UserID != bill.UserID {
		return nil, fmt.Errorf("银行卡不属于该用户")
	}
	if bankCard.CardStatus != model.CardStatusBound {
		return nil, fmt.Errorf("银行卡未绑定，无法进行代扣")
	}

	s.logger.WithFields(
		log.Int("bank_card_id", bankCard.ID),
		log.String("bank_name", bankCard.BankName),
		log.String("action", "use_specified_bank_card"),
	).Info("使用指定银行卡进行手动代扣")
	amount = shopspringutils.CeilToTwoDecimal(amount)
	// 1. 验证还款金额
	if err := s.ValidateRepaymentAmount(billID, amount, withholdType); err != nil {
		s.logger.WithError(err).WithFields(
			log.Int("bill_id", billID),
			log.Float64("amount", amount),
			log.String("withhold_type", withholdType),
			log.String("action", "validate_repayment_amount_failed"),
		).Error("验证还款金额失败")
		return nil, err
	}

	// 3. 使用指定银行卡进行扣款
	result, err := s.processRepayment(
		0, // 手动代扣不需要用户ID验证
		billID,
		bankCard.ID,
		amount,
		withholdType,
		model.TransactionTypeManualWithhold,
		remark,
		adminID,
	)

	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Int("bank_card_id", bankCard.ID),
			log.String("action", "manual_withhold_failed"),
		).Error("指定银行卡扣款失败")
		return nil, fmt.Errorf("银行卡扣款失败: %v", err)
	}

	// 扣款成功，返回结果
	s.logger.WithFields(
		log.String("transaction_no", result.TransactionNo),
		log.Int("bank_card_id", bankCard.ID),
		log.Float64("amount", amount),
		log.String("action", "manual_withhold_success"),
	).Info("手动代扣成功")
	return result, nil
}

// processRepayment 通用还款处理方法
func (s *PaymentService) processRepayment(
	userID int,
	billID int,
	bankCardID int,
	amount float64,
	withholdType string,
	transactionType string,
	remark string,
	adminID int,
) (*PaymentResult, error) {
	// 1. 验证并获取支付相关数据
	bill, bankCard, order, err := s.getPaymentData(billID, bankCardID)
	if err != nil {
		return nil, err
	}

	// 2. 获取系统信息
	systemIP := getSystemIP()
	orderTime := time.Now().Format("**************")

	// 3. 生成交易流水号和支付订单号
	transactionNo := generatePaymentTransactionNo()
	payOrderNo := transactionNo

	// 4. 创建支付
	result, err := s.createSinglePayment(
		bill.UserID,
		bill.OrderID,
		billID,
		amount,
		withholdType,
		bankCard,
		order,
		systemIP,
		orderTime,
		payOrderNo,
		transactionNo,
		transactionType,
	)

	if err != nil {
		return nil, err
	}

	// 5. 对于手动代扣，记录管理员操作日志
	if adminID > 0 {
		operationDetail := map[string]interface{}{
			"transaction_no": result.TransactionNo,
			"amount":         amount,
			"withhold_type":  "MANUAL_WITHHOLD",
			"remark":         remark,
			"bill_id":        billID,
			"bank_card_id":   bankCard.ID,
			"bank_name":      bankCard.BankName,
		}

		detailJSON, _ := json.Marshal(operationDetail)
		operationLog := &model.BusinessOrderOperationLogs{
			OrderID:      bill.OrderID,
			OperatorID:   adminID,
			OperatorName: fmt.Sprintf("管理员%d", adminID),
			Action:       "手动代扣",
			Details:      string(detailJSON),
			CreatedAt:    time.Now().Unix(),
		}

		// 记录操作日志
		if logErr := s.operationModel.CreateLog(operationLog); logErr != nil {
			s.logger.WithError(logErr).WithFields(
				log.Int("order_id", bill.OrderID),
				log.Int("admin_id", adminID),
				log.String("action", "create_operation_log_failed"),
			).Error("记录操作日志失败")
		}
	}

	return result, nil
}

// createSinglePayment 创建单笔支付
func (s *PaymentService) createSinglePayment(
	userID int,
	orderID int,
	billID int,
	amount float64,
	withholdType string,
	bankCard *model.BusinessBankCards,
	order *model.BusinessLoanOrders,
	systemIP string,
	orderTime string,
	payOrderNo string,
	transactionNo string,
	transactionType string,
) (*PaymentResult, error) {
	// 1. 使用传入的交易流水号

	// 2. 创建支付流水记录
	transaction := &model.BusinessPaymentTransactions{
		TransactionNo:     transactionNo,
		ThirdPartyOrderNo: payOrderNo,
		OrderID:           orderID,
		OrderNo:           order.OrderNo,
		BillID:            &billID,
		UserID:            userID,
		BankCardID:        getBankCardIDPtr(bankCard),
		PaymentChannelID:  getPaymentChannelID(order),
		Type:              transactionType,
		WithholdType:      getWithholdTypePtr(withholdType),
		Amount:            model.Decimal(amount),
		Status:            model.TransactionStatusPending,
		CreatedAt:         time.Now(),
	}

	err := s.transactionModel.CreateTransaction(transaction)
	if err != nil {
		return nil, fmt.Errorf("创建支付流水失败: %v", err)
	}

	// todo 以后改成用户id
	merConfig, err := GetMerNoByWithholdType(withholdType)
	if err != nil {
		s.logger.WithFields(
			log.String("withhold_type", withholdType),
			log.String("action", "get_merchant_config_failed"),
			log.ErrorField(err),
		).Error("获取商户配置失败")
		return nil, fmt.Errorf("获取商户配置失败: %v", err)
	}
	paymentRequest := sumpay.NewPayRequest(
		bankCard.ThirdPartyOrderNo,
		systemIP,
		payOrderNo,
		orderTime,
		payOrderNo,
		bankCard.BindCardId,
		amount,
		PaymentGoodsType,
		PaymentGoodsNum,
		merConfig,
	)

	// 4. 调用支付服务并处理结果
	return s.executePaymentAndHandleResult(transactionNo, billID, paymentRequest, amount)
}

// executePaymentAndHandleResult 执行支付并处理结果
func (s *PaymentService) executePaymentAndHandleResult(transactionNo string, billID int, paymentRequest *sumpay.PayRequest, amount float64) (*PaymentResult, error) {
	paymentService, err := sumpay.NewSumpayService(sumpay.WithContext(s.ctx))
	if err != nil {
		return nil, fmt.Errorf("获取支付服务失败: %v", err)
	}

	result, err := paymentService.Pay(paymentRequest)
	if err != nil {
		s.updateTransactionStatus(transactionNo, model.TransactionStatusFailed, "支付调用失败")
		return nil, fmt.Errorf("支付调用失败: %v", err)
	}

	if result != nil {
		if result.RespCode == "000000" {
			// 解析支付响应数据
			payResponseData, parseErr := result.GetPayResponseData()
			if parseErr != nil {
				s.updateTransactionStatus(transactionNo, model.TransactionStatusFailed, "处理响应数据失败")
				return nil, fmt.Errorf("处理响应数据失败: %v", parseErr)
			}
			// 使用新的方法更新交易状态，包含渠道交易号和第三方订单号
			if updateErr := s.updateTransactionStatusWithDetails(transactionNo, model.TransactionStatusSubmitted, "", payResponseData.SerialNo, payResponseData.OrderNo); updateErr != nil {
				return nil, fmt.Errorf("更新交易状态失败: %v", updateErr)
			}
			s.logger.WithFields(
				log.String("transaction_no", transactionNo),
				log.String("third_party_order_no", payResponseData.OrderNo),
				log.String("channel_transaction_no", payResponseData.SerialNo),
				log.String("status", payResponseData.Status),
				log.String("action", "payment_request_submitted"),
			).Info("支付请求提交成功")
			// 查询支付状态
			queryResult, err := s.QueryPaymentStatus(transactionNo)
			if err != nil {
				return nil, fmt.Errorf("查询支付状态失败: %v", err)
			}
			s.logger.WithFields(
				log.String("transaction_no", transactionNo),
				log.String("status", queryResult.Status),
				log.String("action", "query_payment_status"),
			).Info("查询支付状态")

			// 根据查询结果构造返回信息
			var status, message string
			switch queryResult.Status {
			case "success":
				status = "success"
				message = "支付成功"
				s.logger.WithFields(
					log.String("transaction_no", transactionNo),
					log.String("status", queryResult.Status),
					log.String("action", "payment_success"),
				).Info("支付成功")
			case "failed":
				status = "failed"
				message = "支付失败"
				if queryResult.ErrorMessage != "" {
					message = fmt.Sprintf("支付失败: %s", queryResult.ErrorMessage)
				}
			case "submitted":
				status = "submitted"
				message = "支付提交成功"
			default:
				status = "submitted"
				message = "支付提交成功"
			}

			return &PaymentResult{
				TransactionNo: transactionNo,
				Amount:        amount,
				Status:        status,
				Message:       message,
			}, nil
		} else {
			errorMsg := fmt.Sprintf("支付失败: %s", result.RespMsg)
			s.updateTransactionStatus(transactionNo, model.TransactionStatusFailed, errorMsg)
			return nil, fmt.Errorf("%s", errorMsg)
		}
	} else {
		s.updateTransactionStatus(transactionNo, model.TransactionStatusFailed, "支付平台返回空结果")
		return nil, fmt.Errorf("支付平台返回空结果")
	}
}

// getPaymentChannelID 获取支付渠道ID
func getPaymentChannelID(order *model.BusinessLoanOrders) int {
	if order.PaymentChannelID != nil {
		return int(*order.PaymentChannelID)
	}
	return 0
}

// GetSubmittedAmountsByBillID 根据账单ID获取已提交金额
func (s *PaymentService) GetSubmittedAmountsByBillID(billID int) (*SubmittedAmountsResponse, error) {
	// 查询账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return nil, fmt.Errorf("查询账单失败: %v", err)
	}

	// 使用统一的计算逻辑
	submittedGuaranteeAmount, submittedAssetAmount, err := shopspringutils.CalculateSubmittedAmounts(billID)
	if err != nil {
		return nil, err
	}

	// 计算减免后的实际应还金额
	remainingGuaranteeAmount, remainingAssetAmount := shopspringutils.CalculateRemainingAmounts(
		float64(bill.TotalDueAmount),
		float64(bill.TotalWaiveAmount),
		float64(bill.DueGuaranteeFee),
		float64(bill.AssetManagementEntry),
	)
	// 计算剩余担保费和剩余资管费（减免后应还金额 - 已支付金额）
	remainingGuaranteeFee := shopspringutils.SubtractAmountsWithDecimal(remainingGuaranteeAmount, submittedGuaranteeAmount)
	remainingAssetFee := shopspringutils.SubtractAmountsWithDecimal(remainingAssetAmount, submittedAssetAmount)

	s.logger.WithFields(
		log.Int64("bill_id", int64(billID)),
		log.Float64("submitted_guarantee_amount", submittedGuaranteeAmount),
		log.Float64("submitted_asset_amount", submittedAssetAmount),
		log.Float64("remaining_guarantee_amount_after_waive", remainingGuaranteeAmount),
		log.Float64("remaining_asset_amount_after_waive", remainingAssetAmount),
		log.Float64("remaining_guarantee_fee", remainingGuaranteeFee),
		log.Float64("remaining_asset_fee", remainingAssetFee),
		log.String("action", "bill_amount_summary"),
	).Info("账单金额汇总")

	// 构建响应
	response := &SubmittedAmountsResponse{
		BillID:                billID,
		GuaranteeAmount:       ceilToTwoDecimal(submittedGuaranteeAmount), // 已提交担保总和
		AssetAmount:           ceilToTwoDecimal(submittedAssetAmount),     // 已提交资管总和
		TotalAmount:           ceilToTwoDecimal(shopspringutils.AddAmountsWithDecimal(submittedGuaranteeAmount, submittedAssetAmount)),
		RemainingGuaranteeFee: ceilToTwoDecimal(remainingGuaranteeFee), // 剩余担保费
		RemainingAssetFee:     ceilToTwoDecimal(remainingAssetFee),     // 剩余资管费
	}

	return response, nil
}
