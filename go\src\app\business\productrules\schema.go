package productrules

import "fincore/utils/jsonschema"

func commonProductRuleSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "创建产品规则",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"rule_name": {
				Type:        "string",
				Required:    true,
				MaxLength:   100,
				Description: "规则名称",
			},
			"loan_amount": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{0}[0],
				Description: "贷款金额",
			},
			"loan_period": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "贷款期限",
			},
			"total_periods": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "总还款期数",
			},
			"guarantee_fee": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Description: "担保费",
			},
			"annual_interest_rate": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{0}[0],
				Description: "年利率",
			},
			"other_fees": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Description: "其他费用",
			},
			"rule_category": {
				Type:        "string",
				Required:    true,
				Description: "规则类别",
				Enum:        []string{"新用户", "复购用户"},
			},
			"pre_principal": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Description: "前置本金",
			},
			"pre_interest": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Description: "前置利息",
			},
			"pre_guarantee_fee": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Description: "前置担保费",
			},
		},
		Required: []string{"rule_name", "loan_amount", "loan_period", "total_periods", "annual_interest_rate", "rule_category"},
	}
}

// CreateProductRuleSchema 创建产品规则的参数验证规则
func CreateProductRuleSchema() jsonschema.Schema {
	return commonProductRuleSchema()
}

// UpdateProductRuleSchema 编辑产品规则的参数验证规则
func UpdateProductRuleSchema() jsonschema.Schema {
	schema := commonProductRuleSchema()
	minID := float64(1)
	schema.Properties["id"] = jsonschema.ValidateRule{
		Type:        "number",
		Required:    true,
		Min:         &minID,
		Description: "产品规则ID",
	}
	schema.Required = append(schema.Required, "id")
	return schema
}
