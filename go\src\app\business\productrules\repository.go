package productrules

import (
	"context"
	"encoding/json"
	"errors"
	"fincore/model"
	"fincore/utils/gform"
	"fincore/utils/log"
	"fincore/utils/repayment"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/util/gconv"
)

type ProductRuleRepository struct {
	ctx    context.Context
	logger *log.Logger
}

func NewProductRuleRepository(ctx context.Context) *ProductRuleRepository {
	return &ProductRuleRepository{
		ctx:    ctx,
		logger: log.RegisterModule("productrules", "产品规则模块").WithContext(ctx),
	}
}

// InsertProductRuleAndCalculationResult 插入产品规则和规则账单计算结果
func (r *ProductRuleRepository) InsertProductRuleAndCalculationResult(tx gform.IOrm, rule *ProductRule) (err error) {

	if tx == nil {
		tx = model.DB(model.WithContext(r.ctx))
	}
	now := time.Now()
	rule.ProductRule.CreatedAt = &now
	rule.ProductRule.UpdatedAt = &now
	insertDataMap := gconv.Map(rule.ProductRule)
	delete(insertDataMap, "deleted_at")
	calulationResult, _ := json.Marshal(rule.CalculationResult)
	insertDataMap["calculation_result"] = string(calulationResult)

	// 插入产品规则
	_, err = tx.Table("product_rules").Data(insertDataMap).Insert()
	if err != nil {
		r.logger.WithFields(log.String("error", err.Error())).Error("插入产品规则失败")
		return
	}
	return
}

// UpdateProductRuleAndCalculationResult 更新产品规则和规则账单计算结果
func (r *ProductRuleRepository) UpdateProductRuleAndCalculationResult(tx gform.IOrm, rule *ProductRule) (err error) {
	if tx == nil {
		tx = model.DB(model.WithContext(r.ctx))
	}

	updateDataMap := gconv.Map(rule.ProductRule)
	calulationResult, _ := json.Marshal(rule.CalculationResult)
	delete(updateDataMap, "created_at")
	delete(updateDataMap, "updated_at")
	delete(updateDataMap, "deleted_at")
	updateDataMap["calculation_result"] = string(calulationResult)

	// 更新产品规则
	_, err = tx.Table("product_rules").Data(updateDataMap).Where("id", rule.ProductRule.ID).WhereNull("deleted_at").Update()
	if err != nil {
		r.logger.WithFields(log.String("error", err.Error())).Error("更新产品规则失败")
		return fmt.Errorf("更新产品规则失败: %w", err)
	}

	return

}

// GetProductRuleInProgressOrdersParams 查询产品在途订单
type GetProductRuleInProgressOrdersParams struct {
	IDs []uint `json:"ids"`
}

// GetProductRuleInProgressOrders 查询产品在途订单
func (r *ProductRuleRepository) GetProductRuleInProgressOrders(tx gform.IOrm, params GetProductRuleInProgressOrdersParams) (list []gform.Data, err error) {
	if tx == nil {
		tx = model.DB(model.WithContext(r.ctx))
	}
	list, err = tx.Table("business_loan_orders").
		WhereIn("product_rule_id", gconv.Interfaces(params.IDs)).
		WhereIn("status", []interface{}{model.OrderStatusPendingDisbursement, model.OrderStatusDisbursed}).Get()
	if err != nil {
		r.logger.WithFields(log.String("error", err.Error())).Error("查询产品是否存在在途订单失败")
		return
	}

	return
}

// DelProducts 删除产品
func (r *ProductRuleRepository) DelProducts(tx gform.IOrm, IDs []uint) (err error) {
	if tx == nil {
		tx = model.DB(model.WithContext(r.ctx))
	}

	_, err = tx.Table("product_rules").WhereIn("id", gconv.Interfaces(IDs)).Delete()
	return
}

// GetProductRuleByID 根据ID获取产品规则
func (r *ProductRuleRepository) GetProductRuleByID(ID int) (rule *ProductRule, err error) {
	query := model.DB(model.WithContext(r.ctx))
	queryResp, err := query.Table("product_rules").Where("id", ID).WhereNull("deleted_at").First()
	if err != nil {
		return
	}

	if queryResp == nil {
		err = errors.New("产品规则不存在")
		return
	}

	Rule := &model.ProductRules{}
	var CustomerCalculation *repayment.RepaymentSchedule

	err = gconv.Struct(queryResp, &Rule)
	if err != nil {
		return
	}

	if queryResp["calculation_result"] != nil {
		calculationResult := queryResp["calculation_result"].(string)
		err = json.Unmarshal([]byte(calculationResult), &CustomerCalculation)
		if err != nil {
			return

		}
	}

	// 如果没有存储过则进行实时计算
	if CustomerCalculation == nil {
		CustomerCalculation, err = repayment.CalculateRepaymentSchedule(Rule)
		if err != nil {
			return
		}
	}

	rule = &ProductRule{
		ProductRule:       Rule,
		CalculationResult: CustomerCalculation,
	}

	return
}
