const domain = 'https://www.hifincore.com:8080'; //您的域名//独立部署需要填写Go服务api接口域名(部署在Go目录下可以留空)
const localhost = 'https://www.hifincore.com:8080'; //访问本地的域名和端口，如果您改变Go服务端口，请自行修改
window.globalConfig = {
  Main_url: `${domain}/`, //域名
  Root_url: `${domain}/business`, //Api服务器域名
  Upload_url: `${domain}/common`, //Api服务器域名
  Upload_url_dev: `${localhost}/common`, //Api服务器域名-开发环境
  Upload_url_base: domain,
  // Root_url_dev:`${localhost}/business`,//Api服务器域名-开发环境
  Root_url_dev: `/business`, //Api服务器域名-开发环境
  Root_wxurl_dev: `${localhost}`, //接口测试-微信小程序-开发环境
  Upinfile_url: `${domain}/business`, //业务专用上传附件
  Upinfile_url_dev: `${domain}/business`, //业务专用上传附件-开发环境
  AppTitle: '悦心易管理系统',
  AppVersion: '1.0.0',
  Company: '悦心易',
  Address: '中国·长沙',
  Team: 'FinCore技术团队',
  loginTitle: '登录悦心易管理系统',
  loginSubTitle: '管理账号、业务、数据统计、触达事件等',
  Copyright: 'GoFly团队提供技术支持',
  MaxSizeImage: 5, //最大上传图片大小,单位M
  MaxSizeVideo: 150, //最大上传视频大小,单位M
};
