package lock

import (
	"fincore/utils/log"
	"fmt"
	"time"
)

// GetBillOrderLock 获取账单订单同步锁
func GetBillOrderLock(billID, orderID int, logger *log.Logger) Lock {
	lockKey := fmt.Sprintf("bill_order_lock_%d_%d", billID, orderID)
	if logger != nil {
		return GetLockWithLogger(lockKey, logger, 30*time.Second)
	}
	return GetLock(lockKey, 30*time.Second)
}

// GetOrderTransactionLock 流水记录锁
// todo 因有 list 查询，暂以订单维度加锁，后续优化
func GetOrderTransactionLock(orderID int, logger *log.Logger) Lock {
	lockKey := fmt.Sprintf("order_transaction_lock_%d", orderID)
	if logger != nil {
		return GetLockWithLogger(lockKey, logger, 10*time.Second)
	}
	return GetLock(lockKey, 30*time.Second)
}

// GetPaymentRefreshLock 支付刷新锁
func GetPaymentRefreshLock(transactionNo string, logger *log.Logger) Lock {
	lockKey := fmt.Sprintf("payment_refresh_lock_%s", transactionNo)
	if logger != nil {
		return GetLockWithLogger(lockKey, logger, 30*time.Second)
	}
	return GetLock(lockKey, 30*time.Second)
}

// GetRefundRefreshLock 退款刷新锁
func GetRefundRefreshLock(refundTransactionID int, logger *log.Logger) Lock {
	lockKey := fmt.Sprintf("refund_refresh_lock_%d", refundTransactionID)
	if logger != nil {
		return GetLockWithLogger(lockKey, logger, 30*time.Second)
	}
	return GetLock(lockKey, 30*time.Second)
}

// GetDisbursementRefreshLock 放款刷新锁
func GetDisbursementRefreshLock(orderNo string, logger *log.Logger) Lock {
	lockKey := fmt.Sprintf("disbursement_refresh_lock_%s", orderNo)
	if logger != nil {
		return GetLockWithLogger(lockKey, logger, 30*time.Second)
	}
	return GetLock(lockKey, 30*time.Second)
}
