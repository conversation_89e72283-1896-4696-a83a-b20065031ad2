package hetong

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	businessorder "fincore/app/business/order"
	Service "fincore/app/dianziqian/service"

	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/log"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

/**
*使用 Index 是省略路径中的index
*本路径为： /dianziqian/hetong/xx -省去了index
 */
func init() {
	gf.Register(&Index{}, reflect.TypeOf(Index{}).PkgPath())
}

type Index struct {
}

// 合同创建请求参数结构体
// 可根据实际需要添加更多字段
// 这里只保留 user_id 和 bill_id 示例
// 若有 name/iron_id 等需求可自行扩展
type CreateContractRequest struct {
	UserID      int    `json:"user_id" binding:"required"`
	ProductID   int    `json:"product_id" binding:"required"`
	UserName    string `json:"user_name" binding:"required"` // 用户姓名
	ChannelCode string `json:"channel_code"`                 // 渠道ID
	BankCardID  int    `json:"bank_card_id"`                 // 放款卡id
}

type UpdateContractRequest struct {
	ContractNo string `json:"contract_no" binding:"required"`
}

type GetContractStatusRequest struct {
	ContractNo string `json:"contract_no" binding:"required"`
}

/*
*
*《创建待签署的合同，得到可预览的合同的URL以及合同》
前端需要上传签署的用户名、以及要还款的期数。
在测试中，先传一个姓名先与身份证先。
*/
func (api *Index) CreateContract(c *gin.Context) {
	var req CreateContractRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		results.Failed(c, "参数错误: "+err.Error(), nil)
		return
	}

	if req.BankCardID == 0 {
		results.Failed(c, "放款卡id不能为空", nil)
		return
	}

	//这儿可以加打印日志接口。
	log.Info("接收到前端的请求，开始打印请求体")
	log.Info("user_id: %d, ProductID: %d, User_name: %s\n", req.UserID, req.ProductID, req.UserName)

	//id可以查姓名，但为了避免传错值，做下id与对应姓名的匹配，避免数据库数据不对时导致的问题
	//根据id，查询其一系列数据，包含银行卡，紧急联系人等。

	//查询用户信息，获取用户的id，以及是否完成实名认证。
	borrower, err := GetCompleteBorrowerInfo(c, req.UserID, req.UserName)
	if err != nil {
		results.Failed(c, "获取用户信息失败: "+err.Error(), nil)
		return
	}
	// 检查用户是否完成实名认证
	SerialNo, err := CheckRealNameAuth(borrower.Name, borrower.IDCard)
	borrower.SerialNo = SerialNo
	if err != nil {
		results.Failed(c, "未完成实名认证: "+err.Error(), nil)
		return
	}
	// 将用户添加到爱签中

	err = RegisterUser(borrower.IDCard, borrower.SerialNo)
	if err != nil {
		// 注册失败
		log.Error("注册用户失败：%s", err)
		results.Failed(c, "注册用户失败: "+err.Error(), nil)
		return
	} else {
		// 注册成功
		log.Info("注册用户成功")
	}
	//根据产品id，生成相关的担保费等信息。
	rule_service := model.GetProductRulesService()
	// 调用方法获取产品规则
	rule, err := rule_service.GetProductRuleByID(req.ProductID)
	if err != nil {
		log.Error("获取产品规则失败: %v\n", err)
		results.Failed(c, "获取产品规则失败: "+err.Error(), nil)
		return
	}

	repay_schedule, err := businessorder.GetProductRuleCalculationResult(rule)
	if err != nil {
		log.Error("计算还款计划失败: %v", err)
		results.Failed(c, "计算还款计划失败: "+err.Error(), nil)
		return
	}
	log.Info("还款计划计算成功，开始生成合同%s", repay_schedule)
	//

	// 生成合同编号：借款人姓名+时间戳
	contractNo := fmt.Sprintf("%s_%d", borrower.Name, time.Now().Unix())
	// 准备合同创建的JSON数据
	createFileJson := CreatePrepareContract(borrower, rule, repay_schedule, contractNo)

	// 调用服务创建待签合同
	CreatResult := Service.CreateFile(createFileJson, nil)
	var res_parameter BaseResponse
	if err := json.Unmarshal(CreatResult, &res_parameter); err != nil {
		log.Error("解析失败:", err)
		results.Failed(c, "生成合同对端返回结果解析失败", nil)
		return
	}

	if res_parameter.Code != 100000 {
		log.Error("合同生成失败，错误码: %d, 错误信息: %s", res_parameter.Code, res_parameter.Msg)
		results.Failed(c, "合同生成失败", res_parameter.Msg)
		return
	}

	addSignerConfigJson := addSignerinfo(borrower, contractNo)
	signResult := Service.AddSigner(addSignerConfigJson)
	var sign_Result ContractResponse
	if err := json.Unmarshal(signResult, &sign_Result); err != nil {
		log.Error("解析失败:", err)
		results.Failed(c, "生成合同签署链接失败", nil)
		return
	}
	if sign_Result.Code != 100000 {
		results.Failed(c, "生成合同签署链接失败", nil)
		return
	}
	contractService := model.NewContractService(c)

	// 先批量作废该用户的未签署合同
	if err := contractService.VoidUnsignedContractsByUserID(req.UserID); err != nil {
		log.Error("批量作废未签署合同失败:", err)
		// 这里可以选择继续或返回，我建议继续，不影响新合同生成
	}
	// 构造新合同数据
	newContract := &model.Contract{
		ContractID:      contractNo,                           // 合同编号
		UserID:          req.UserID,                           // 用户ID
		Username:        borrower.Name,                        // 用户真实姓名
		IDCard:          borrower.IDCard,                      // 用户身份证
		BankCardID:      req.BankCardID,                       // 放款卡id
		ProductID:       req.ProductID,                        // 产品ID
		ContractFileURL: sign_Result.Data.SignUser[0].SignUrl, // 合同文件URL
		ContractStatus:  model.ContractStatusUnsigned,         // 默认未签署
		CreateTime:      time.Now().Unix(),
		UpdateTime:      time.Now().Unix(),
		ChannelCode:     req.ChannelCode, // 渠道ID
	}

	// 创建合同
	id, err := contractService.CreateContract(newContract)
	if err != nil {
		log.Error("创建合同失败:", err)
		results.Failed(c, "创建合同失败: "+err.Error(), nil)
		return
	}

	log.Info("新合同创建成功，合同ID: %d\n", id)

	// 返回成功响应
	results.Success(c, "合同生成成功", sign_Result.Data.SignUser[0].SignUrl, nil)
	return
}

func (api *Index) UpdateContract(c *gin.Context) {
	var req UpdateContractRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		results.Failed(c, "参数错误: "+err.Error(), nil)
		return
	}
	//这儿可以加打印日志接口。
	log.Info(string("接收到前端的请求，开始打印请求体"))
	log.Info("ContractNo: %s\n", req.ContractNo)

	contractService := model.NewContractService(c)

	err := contractService.UpdateToSignedbyContractNo(req.ContractNo)
	if err != nil {
		log.Error("合同状态更新失败", err)
		results.Failed(c, "合同状态更新失败: "+err.Error(), nil)
		return
	}
	results.Success(c, "合同状态更新成功", nil, nil)
}

func (api *Index) QueryContractStatus(c *gin.Context) {
	var req GetContractStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		results.Failed(c, "参数错误: "+err.Error(), nil)
		return
	}

	// 打印日志

	// 调用查询
	resp, err := QueryContractStatus(req.ContractNo)
	if err != nil {
		results.Failed(c, "查询合同状态失败: "+err.Error(), nil)
		return
	}

	results.Success(c, "查询成功", resp.Data.Status, nil)
}
